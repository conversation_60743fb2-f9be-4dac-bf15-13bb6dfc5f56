{% extends 'base.html' %}

{% block title %}Production SMTP Test{% endblock %}

{% block content %}
<div class="container mx-auto mt-10 p-5 max-w-md">
    <div class="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4">
        <h1 class="text-2xl font-bold mb-6 text-center text-gray-800">🚀 Production SMTP Test</h1>
        
        {% if not g.is_admin %}
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            <strong>Access Denied:</strong> Admin privileges required to test SMTP configuration.
        </div>
        <div class="text-center">
            <a href="{{ url_for('home') }}" class="text-blue-500 hover:text-blue-700">Back to Home</a>
        </div>
        {% else %}
        
        <p class="mb-6 text-gray-600 text-center">
            Test your production SMTP configuration to ensure emails are working correctly.
        </p>

        <div id="testResult" class="mb-4 hidden"></div>

        <form id="smtpTestForm">
            <div class="mb-4">
                <label class="block text-gray-700 text-sm font-bold mb-2" for="test_email">
                    Test Email Address
                </label>
                <input 
                    class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline focus:border-blue-500" 
                    id="test_email" 
                    name="test_email" 
                    type="email" 
                    placeholder="Enter email to receive test"
                    value="<EMAIL>"
                    required
                >
            </div>
            
            <div class="flex items-center justify-between">
                <button 
                    class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline w-full" 
                    type="submit"
                    id="testBtn"
                >
                    🧪 Test SMTP Configuration
                </button>
            </div>
        </form>
        
        <div class="text-center mt-6">
            <a href="{{ url_for('home') }}" class="text-blue-500 hover:text-blue-700 text-sm">
                Back to Home
            </a>
        </div>
        
        <div class="mt-6 p-4 bg-gray-100 rounded">
            <h3 class="font-bold text-sm mb-2">What this test does:</h3>
            <ul class="text-xs text-gray-600 list-disc list-inside">
                <li>Checks environment variables (ZOHO_SMTP_PASSWORD, SECRET_KEY)</li>
                <li>Tests SMTP connection to smtppro.zoho.com</li>
                <li>Sends a test <NAME_EMAIL></li>
                <li>Verifies production email configuration</li>
            </ul>
        </div>
        
        {% endif %}
    </div>
</div>

<script>
{% if g.is_admin %}
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('smtpTestForm');
    const testBtn = document.getElementById('testBtn');
    const testResult = document.getElementById('testResult');
    
    form.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const email = document.getElementById('test_email').value;
        
        // Disable button and show loading
        testBtn.disabled = true;
        testBtn.textContent = '🔄 Testing...';
        
        // Clear previous results
        testResult.className = 'mb-4 hidden';
        testResult.innerHTML = '';
        
        try {
            const response = await fetch('/test-smtp', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': '{{ csrf_token() }}'
                },
                body: JSON.stringify({ email: email })
            });
            
            const result = await response.json();
            
            // Show result
            testResult.className = 'mb-4 p-4 rounded border';
            
            if (result.success) {
                testResult.className += ' bg-green-100 border-green-400 text-green-700';
                testResult.innerHTML = `
                    <div class="font-bold">✅ SMTP Test Successful!</div>
                    <div class="mt-2">${result.message}</div>
                    <div class="mt-2 text-sm">
                        <strong>Environment Status:</strong>
                        <ul class="list-disc list-inside mt-1">
                            <li>ZOHO_SMTP_PASSWORD: ${result.environment_status?.ZOHO_SMTP_PASSWORD?.exists ? '✅ Found' : '❌ Missing'} (${result.environment_status?.ZOHO_SMTP_PASSWORD?.length || 0} chars)</li>
                            <li>SECRET_KEY: ${result.environment_status?.SECRET_KEY?.exists ? '✅ Found' : '❌ Missing'} (${result.environment_status?.SECRET_KEY?.length || 0} chars)</li>
                        </ul>
                    </div>
                    <div class="mt-2 text-sm">
                        📧 Check your inbox at <strong>${email}</strong> for the test email.
                    </div>
                `;
            } else {
                testResult.className += ' bg-red-100 border-red-400 text-red-700';
                testResult.innerHTML = `
                    <div class="font-bold">❌ SMTP Test Failed</div>
                    <div class="mt-2">${result.message}</div>
                    ${result.environment_status ? `
                    <div class="mt-2 text-sm">
                        <strong>Environment Status:</strong>
                        <ul class="list-disc list-inside mt-1">
                            <li>ZOHO_SMTP_PASSWORD: ${result.environment_status.ZOHO_SMTP_PASSWORD?.exists ? '✅ Found' : '❌ Missing'} (${result.environment_status.ZOHO_SMTP_PASSWORD?.length || 0} chars)</li>
                            <li>SECRET_KEY: ${result.environment_status.SECRET_KEY?.exists ? '✅ Found' : '❌ Missing'} (${result.environment_status.SECRET_KEY?.length || 0} chars)</li>
                        </ul>
                    </div>
                    ` : ''}
                `;
            }
            
        } catch (error) {
            console.error('SMTP test error:', error);
            testResult.className = 'mb-4 p-4 rounded border bg-red-100 border-red-400 text-red-700';
            testResult.innerHTML = `
                <div class="font-bold">❌ Test Error</div>
                <div class="mt-2">Failed to run SMTP test: ${error.message}</div>
            `;
        } finally {
            // Re-enable button
            testBtn.disabled = false;
            testBtn.textContent = '🧪 Test SMTP Configuration';
        }
    });
});
{% endif %}
</script>
{% endblock %}
