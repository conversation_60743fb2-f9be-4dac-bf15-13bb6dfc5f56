<!DOCTYPE html>
{% if not request.endpoint %}
  <script>window.location.href = "/";</script>
{% endif %}
{% if not request.endpoint %}
  <script>window.location.href = "/";</script>
{% endif %}
<html lang="he" dir="rtl">
<head>
<meta charset="UTF-8">
<title>Register</title>
<style>
body {
    font-family: sans-serif;
    margin: 20px;
    direction: rtl;
    text-align: right;
}
.auth-container {
    max-width: 400px;
    margin: 0 auto;
    padding: 20px;
}
.auth-section {
    margin-bottom: 30px;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 8px;
}
.form-group {
    margin-bottom: 15px;
}
.form-group label {
    display: block;
    margin-bottom: 5px;
}
.form-group input {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    direction: ltr;
    text-align: left;
}
button {
    width: 100%;
    padding: 10px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
}
#registerBtn {
    background-color: #28a745;
    color: white;
}
.error-message {
    color: red;
    margin-top: 10px;
    display: none;
}
.login-link {
    text-align: center;
    margin-top: 20px;
}
</style>
</head>
<body>
  <div class="auth-container">
    <h1>הרשמה</h1>

    <div class="auth-section">
      <form id="registerForm">
        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
        <div class="form-group">
          <label for="firstName">שם פרטי:</label>
          <input type="text" id="firstName" required dir="rtl" style="text-align: right; direction: rtl;">
        </div>
        <div class="form-group">
          <label for="lastName">שם משפחה:</label>
          <input type="text" id="lastName" required dir="rtl" style="text-align: right; direction: rtl;">
        </div>
        <div class="form-group">
          <label for="email">אימייל:</label>
          <input type="email" id="email" required>
        </div>
        <div class="form-group">
          <label for="password">סיסמה:</label>
          <input type="password" id="password" required>
        </div>
        <div class="form-group">
          <label for="confirmPassword">אימות סיסמה:</label>
          <input type="password" id="confirmPassword" required>
        </div>
        <div class="form-group" style="display: flex; align-items: flex-start;">
          <input type="checkbox" id="termsCheckbox" style="width: auto; margin-left: 10px;">
          <label for="termsCheckbox">מאשר את תנאי השימוש ומדיניות הפרטיות של האתר</label>
        </div>
        <button type="submit" id="registerBtn">הרשמה</button>
        <p id="registerError" class="error-message"></p>
      </form>
    </div>

    <!-- <div class="login-link">
      <p>כבר יש לך חשבון? <a href="/login">התחבר כאן</a></p>
    </div>
  </div> -->

  <!-- Add Firebase SDKs -->
  <script src="https://www.gstatic.com/firebasejs/9.6.1/firebase-app-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/9.6.1/firebase-auth-compat.js"></script>

  <script>
    // Fetch Firebase configuration from server
    fetch('/get_firebase_config')
      .then(response => response.json())
      .then(firebaseConfig => {
        // Initialize Firebase
        const app = firebase.initializeApp(firebaseConfig);
        const auth = firebase.auth();

    // UI Elements
    const registerForm = document.getElementById('registerForm');
    const firstNameInput = document.getElementById('firstName');
    const lastNameInput = document.getElementById('lastName');
    const emailInput = document.getElementById('email');
    const passwordInput = document.getElementById('password');
    const confirmPasswordInput = document.getElementById('confirmPassword');
    const termsCheckbox = document.getElementById('termsCheckbox');
    const registerError = document.getElementById('registerError');

    // Function to get CSRF token
    async function getCsrfToken() {
      try {
        const response = await fetch('/get_csrf_token');
        const data = await response.json();
        return data.csrf_token;
      } catch (error) {
        console.error('Error getting CSRF token:', error);
        return 'csrf-token-placeholder';
      }
    }

    // Registration handler
    registerForm.addEventListener('submit', async (e) => {
  e.preventDefault();

  const email = emailInput.value;
  const password = passwordInput.value;
  const confirmPassword = confirmPasswordInput.value;
  const firstName = firstNameInput.value;
  const lastName = lastNameInput.value;

  registerError.style.display = 'none';

  if (!termsCheckbox.checked) {
    registerError.textContent = 'יש לאשר את תנאי השימוש ומדיניות הפרטיות';
    registerError.style.display = 'block';
    return;
  }

  if (password !== confirmPassword) {
    registerError.textContent = 'הסיסמאות אינן תואמות';
    registerError.style.display = 'block';
    return;
  }

  try {
    console.log("🔐 Creating user...");
    const userCredential = await auth.createUserWithEmailAndPassword(email, password);
    const user = userCredential.user;
    // console.log("✅ User created:", user.email);

    await user.updateProfile({
      displayName: `${firstName} ${lastName}`
    });
    console.log("📝 Profile updated");

    // Get the ID token to authenticate with backend
    const idToken = await user.getIdToken();

    // Set the token in session (allow unverified for registration flow)
    const tokenResponse = await fetch('/set_token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        token: idToken,
        allow_unverified: true  // Allow unverified users during registration
      })
    });

    if (tokenResponse.ok) {
      // Send verification email via backend
      const emailResponse = await fetch('/send-verification-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': await getCsrfToken()
        }
      });

      const emailResult = await emailResponse.json();
      if (emailResult.success) {
        console.log("📧 Verification email sent via backend to:", user.email);
        alert('Registration successful! Please check your email and verify your account before logging in.');
      } else {
        console.error("Failed to send verification email:", emailResult.message);
        alert('Registration successful, but there was an issue sending the verification email. Please try resending it from the verification page.');
      }
    } else {
      console.error("Failed to set session token");
      alert('Registration successful, but there was an issue with authentication. Please try logging in.');
    }

    window.location.href = "/";
  } catch (error) {
    console.error("🔥 Registration error:", error);
    let errorMessage = 'אירעה שגיאה במהלך ההרשמה. נסה שוב מאוחר יותר.';
    if (error.code === 'auth/email-already-in-use') {
      errorMessage = 'כתובת האימייל הזו כבר רשומה. אנא התחבר או השתמש באימייל אחר.';
    } else if (error.code === 'auth/weak-password') {
      errorMessage = 'הסיסמה חלשה מדי. אנא בחר סיסמה חזקה יותר.';
    } else if (error.message) {
      errorMessage = error.message;
    }
    registerError.textContent = errorMessage;
    registerError.style.display = 'block';
  }
    });
  })
  .catch(error => {
    console.error("🔥 Firebase config fetch error:", error);
    const registerError = document.getElementById('registerError');
    if (registerError) {
      registerError.textContent = 'אירעה שגיאה בטעינת ההגדרות. נסה לרענן את הדף.';
      registerError.style.display = 'block';
    }
  });
  </script>
</body>
</html>