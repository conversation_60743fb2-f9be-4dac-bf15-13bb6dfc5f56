{% if not request.endpoint %}
  <script>window.location.href = "/";</script>
{% endif %}
{% if not request.endpoint %}
  <script>window.location.href = "/";</script>
{% endif %}
<style>
  body {
      font-family: sans-serif;
      margin: 20px;
      direction: rtl;
      text-align: right;
  }

  .auth-container {
      max-width: 90%;
      width: 400px;
      margin: 0 auto;
      padding: 20px;
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 4px 8px rgba(0,0,0,0.2);
  }

  .auth-section {
      margin-bottom: 30px;
      padding: 20px;
      border: 1px solid #ddd;
      border-radius: 8px;
  }

  .auth-divider {
      text-align: center;
      margin: 20px 0;
      position: relative;
  }

  .auth-divider::before {
      content: "";
      position: absolute;
      top: 50%;
      left: 0;
      right: 0;
      height: 1px;
      background: #ddd;
      z-index: -1;
  }

  .auth-divider span {
      background: white;
      padding: 0 10px;
      color: #666;
  }

  .form-group {
      margin-bottom: 15px;
  }

  .form-group label {
      display: block;
      margin-bottom: 8px;
      font-size: 16px;
  }

  .form-group input {
      width: 100%;
      padding: 12px;
      border: 1px solid #ddd;
      border-radius: 4px;
      direction: ltr;
      text-align: left;
      font-size: 16px;
      min-height: 44px;
  }

  button {
      width: 100%;
      padding: 12px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
      min-height: 44px;
      touch-action: manipulation;
  }

  #loginBtn {
      background-color: #4285f4;
      color: white;
  }

  #login-button {
      background-color: #28a745;
      color: white;
  }

  .error-message {
      color: red;
      margin-top: 10px;
      display: none;
  }

  .message {
      margin-top: 10px;
      padding: 8px;
      border-radius: 4px;
      text-align: center;
      font-size: 14px;
      line-height: 1.4;
  }

  .success-message {
      color: #28a745;
      background-color: rgba(40, 167, 69, 0.1);
      border: 1px solid rgba(40, 167, 69, 0.2);
  }

  .error-message {
      color: #dc3545;
      background-color: rgba(220, 53, 69, 0.1);
      border: 1px solid rgba(220, 53, 69, 0.2);
  }

  /* Ensure both modals have the same styling */
  #loginModal, #resetPasswordModal {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      z-index: 1000;
      justify-content: center;
      align-items: center;
  }

  .modal {
      background-color: #fff;
  }

  /* Override the base.html modal styles */
  #loginModal {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      z-index: 1000;
      justify-content: center;
      align-items: center;
  }

  /* Style for the modal content */
  .auth-container {
      background-color: white !important;
      max-width: 90%;
      width: 400px;
      margin: 0 auto;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 4px 8px rgba(0,0,0,0.2);
  }

  /* Add explicit white color to button text */
  button.login-button,
  button.google-button {
      color: white !important;
      font-weight: bold;
  }

  /* Close button - made touch-friendly */
  .close-modal {
      float: left;
      cursor: pointer;
      font-size: 24px;
      color: red;
      padding: 8px;
      min-width: 44px;
      min-height: 44px;
      display: flex;
      align-items: center;
      justify-content: center;
  }

  /* Media queries for responsive design */
  @media (max-width: 640px) {
      .auth-container {
          padding: 15px;
      }

      .auth-section {
          padding: 15px;
      }
  }
</style>
<div id="loginModal" class="modal">
<div class="auth-container">
  <span id="closeLoginModal" class="close-modal">&times;</span>
  <h2 class="text-xl font-bold mb-4">התחברות</h2>

  <div class="auth-section">
    <!-- Email/Password Login Section -->
    <form id="emailLoginForm" class="login-form">
      <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
      <div class="form-group">
        <label for="modalEmail">אימייל:</label>
        <input type="email" id="modalEmail" name="email" required>
      </div>
      <div class="form-group">
        <label for="modalPassword">סיסמה:</label>
        <input type="password" id="modalPassword" name="password" required>
      </div>
      <button type="submit" class="login-button" style="background-color: #28a745;">התחבר באמצעות אימייל</button>
      <p id="modalEmailError" class="error-message"></p>
    </form>
    <div class="text-center mt-4">
      <p>אין לך חשבון? <a href="javascript:void(0);" class="text-blue-600 underline" id="registerLink">הירשם כאן</a></p>
      <p class="mt-2">שכחת סיסמה? <a href="javascript:void(0);" class="text-blue-600 underline" id="resetPasswordLink">איפוס סיסמה</a></p>
    </div>
    <div class="auth-divider">
      <span>או</span>
    </div>

    <div class="auth-section">
      <button id="modalGoogleLoginBtn" class="google-button" style="background-color: #4285f4;">התחבר באמצעות Google</button>
      <p id="modalGoogleError" class="error-message"></p>
    </div>
  </div>
</div>
</div>

<!-- Password Reset Modal -->
<div id="resetPasswordModal" class="modal">
  <div class="auth-container">
    <span id="closeResetModal" class="close-modal">&times;</span>
    <h2 class="text-xl font-bold mb-4">איפוס סיסמה</h2>

    <div class="auth-section">
      <form id="resetPasswordForm" class="login-form">
        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
        <div class="form-group">
          <label for="resetEmail">אימייל:</label>
          <input type="email" id="resetEmail" name="email" required>
        </div>
        <button type="submit" class="login-button" style="background-color: #4285f4;">שלח קישור לאיפוס סיסמה</button>
        <p id="resetEmailMessage" class="message" style="margin-top: 10px; display: none; text-align: center;"></p>
      </form>
      <div class="text-center mt-4">
        <p><a href="javascript:void(0);" class="text-blue-600 underline" id="backToLoginLink">חזרה להתחברות</a></p>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', () => {
console.log("Login modal script loaded");

const loginBtn = document.getElementById('loginBtn');
const closeModalBtn = document.getElementById('closeLoginModal');
const loginModal = document.getElementById('loginModal');
const registerLink = document.getElementById('registerLink');

if (loginBtn) {
  loginBtn.addEventListener('click', () => {
    loginModal.style.display = 'flex';
  });
}

// Check that closeModalBtn exists before adding event listener
if (closeModalBtn) {
  closeModalBtn.addEventListener('click', () => {
    loginModal.style.display = 'none';
  });
}

// Add event listener for register link
if (registerLink) {
  registerLink.addEventListener('click', (e) => {
    e.preventDefault(); // Prevent default link behavior
    // Close the modal before navigating to register page
    loginModal.style.display = 'none';
    // Navigate to register page
    window.location.href = '/register';
  });
}

// Password reset functionality
const resetPasswordLink = document.getElementById('resetPasswordLink');
const resetPasswordModal = document.getElementById('resetPasswordModal');
const closeResetModal = document.getElementById('closeResetModal');
const backToLoginLink = document.getElementById('backToLoginLink');
const resetPasswordForm = document.getElementById('resetPasswordForm');
const resetEmailMessage = document.getElementById('resetEmailMessage');

// Show reset password modal
if (resetPasswordLink) {
  resetPasswordLink.addEventListener('click', (e) => {
    e.preventDefault();
    loginModal.style.display = 'none';
    resetPasswordModal.style.display = 'flex';
  });
}

// Close reset password modal
if (closeResetModal) {
  closeResetModal.addEventListener('click', () => {
    resetPasswordModal.style.display = 'none';
  });
}

// Back to login link
if (backToLoginLink) {
  backToLoginLink.addEventListener('click', () => {
    resetPasswordModal.style.display = 'none';
    loginModal.style.display = 'flex';
  });
}

// Handle reset password form submission
if (resetPasswordForm) {
  resetPasswordForm.addEventListener('submit', (e) => {
    e.preventDefault();

    const email = document.getElementById('resetEmail').value;
    const csrfToken = document.querySelector('input[name="csrf_token"]').value;
    const resetButton = resetPasswordForm.querySelector('button[type="submit"]');
    const originalButtonText = resetButton.textContent;

    // Validate email
    if (!email || !email.includes('@')) {
      resetEmailMessage.textContent = 'אנא הזן כתובת אימייל תקינה';
      resetEmailMessage.style.display = 'block';
      resetEmailMessage.className = 'message error-message';
      return;
    }

    // Clear previous messages
    resetEmailMessage.textContent = '';
    resetEmailMessage.style.display = 'none';
    resetEmailMessage.className = 'message';

    // Show loading state
    resetButton.disabled = true;
    resetButton.textContent = 'שולח...';

    console.log("Sending password reset request for:", email);

    // Use backend password reset instead of Firebase
    console.log("Using backend password reset");
    sendServerSidePasswordReset();

    // Function to send server-side password reset request
    function sendServerSidePasswordReset() {
      fetch('/reset_password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': csrfToken
        },
        body: JSON.stringify({ email: email })
      })
      .then(response => {
        console.log("Password reset response status:", response.status);
        return response.json();
      })
      .then(data => {
        console.log("Password reset response data:", data);

        // Reset button state
        resetButton.disabled = false;
        resetButton.textContent = originalButtonText;

        // Show success message
        resetEmailMessage.textContent = data.message;
        resetEmailMessage.style.display = 'block';
        resetEmailMessage.className = 'message success-message';

        // Clear the form
        document.getElementById('resetEmail').value = '';

        // Automatically return to login after 5 seconds
        setTimeout(() => {
          resetPasswordModal.style.display = 'none';
          loginModal.style.display = 'flex';
        }, 5000);
      })
      .catch(error => {
        // Reset button state
        resetButton.disabled = false;
        resetButton.textContent = originalButtonText;

        console.error('Error sending password reset:', error);
        resetEmailMessage.textContent = 'אירעה שגיאה. אנא נסה שוב מאוחר יותר.';
        resetEmailMessage.style.display = 'block';
        resetEmailMessage.className = 'message error-message';
      });
    }
  });
}

window.addEventListener('click', (e) => {
  if (e.target === loginModal) {
    loginModal.style.display = 'none';
  }
  if (e.target === resetPasswordModal) {
    resetPasswordModal.style.display = 'none';
  }
});

// Make sure Firebase is initialized before using it
// Firebase will be initialized by base.html, so we just need to wait for it
const waitForFirebase = () => {
  if (typeof firebase !== 'undefined' && firebase.apps.length > 0) {
    initializeAuthListeners();
  } else {
    setTimeout(waitForFirebase, 100);
  }
};

waitForFirebase();

function initializeAuthListeners() {
  // Email/Password Sign In
  document.getElementById('emailLoginForm').addEventListener('submit', function(e) {
  e.preventDefault();

  console.log("Email login form submitted");
  const email = document.getElementById('modalEmail').value;
  const password = document.getElementById('modalPassword').value;
  const csrfToken = document.querySelector('input[name="csrf_token"]').value;

  document.getElementById('modalEmailError').textContent = '';
  document.getElementById('modalEmailError').style.display = 'none';

  console.log("Attempting to sign in with email:", email);

  firebase.auth().signInWithEmailAndPassword(email, password)
    .then(userCredential => {
      const user = userCredential.user;
      console.log("User signed in successfully:", user.email);

      user.getIdToken().then(idToken => {
        console.log("Got ID token, setting server session");
        fetch('/set_token', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken
          },
          body: JSON.stringify({ token: idToken })
        })
        .then(response => {
          if (response.ok) {
            console.log("Token set successfully, reloading page");
            window.location.reload();
          } else {
            console.error("Failed to set token on server");
            document.getElementById('modalEmailError').textContent = 'Failed to authenticate. Try again.';
            document.getElementById('modalEmailError').style.display = 'block';
          }
        })
        .catch(err => {
          console.error('Error setting token:', err);
          document.getElementById('modalEmailError').textContent = 'An error occurred. Please try again.';
          document.getElementById('modalEmailError').style.display = 'block';
        });
      });
    })
    .catch(error => {
      console.error("Login error:", error.code, error.message);
      document.getElementById('modalEmailError').textContent = error.message;
      document.getElementById('modalEmailError').style.display = 'block';
    });
});

// Google Sign In
document.getElementById('modalGoogleLoginBtn').addEventListener('click', () => {
  console.log("Google login button clicked");

  const csrfToken = document.querySelector('input[name="csrf_token"]').value;
  document.getElementById('modalGoogleError').textContent = '';
  document.getElementById('modalGoogleError').style.display = 'none';

  const provider = new firebase.auth.GoogleAuthProvider();
  console.log("Initiating Google sign in popup");

  firebase.auth().signInWithPopup(provider)
    .then(result => {
      if (result.credential) {
        console.log("Google sign in successful:", result.user.email);

        result.user.getIdToken().then(idToken => {
          console.log("Got Google ID token, setting server session");
          fetch('/set_token', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'X-CSRFToken': csrfToken
            },
            body: JSON.stringify({ token: idToken })
          })
          .then(response => {
            if (response.ok) {
              console.log("Token set successfully, reloading page");
              window.location.reload();
            } else {
              console.error("Failed to set token on server");
              document.getElementById('modalGoogleError').textContent = 'Failed to authenticate. Please try again.';
              document.getElementById('modalGoogleError').style.display = 'block';
            }
          })
          .catch(err => {
            console.error("Error setting token:", err);
            document.getElementById('modalGoogleError').textContent = 'An error occurred. Please try again.';
            document.getElementById('modalGoogleError').style.display = 'block';
          });
        });
      } else {
        console.error("No credential returned from Google sign in");
        document.getElementById('modalGoogleError').textContent = 'Authentication failed. Please try again.';
        document.getElementById('modalGoogleError').style.display = 'block';
      }
    })
    .catch(error => {
      console.error("Error signing in with Google:", error.code, error.message);
      document.getElementById('modalGoogleError').textContent = error.message;
      document.getElementById('modalGoogleError').style.display = 'block';
    });
  });
}
});
</script>