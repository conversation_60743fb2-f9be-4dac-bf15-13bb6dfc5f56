#!/usr/bin/env python3
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import os
from getpass import getpass
from dotenv import load_dotenv
from utils.encryption_utils import decrypt_id # Import for decryption

# Load environment variables from .env file
load_dotenv()

def send_email(recipient, subject, message, smtp_password=None, debug=False):
    """
    Send an email using smtplib with Zoho SMTP configuration

    Args:
        recipient (str): Email address of the recipient
        subject (str): Subject line of the email
        message (str): Body text of the email (can be HTML)
        smtp_password (str, optional): SMTP password. If not provided, will use from .env
        debug (bool): Enable debug output for troubleshooting

    Returns:
        bool: True if email was sent successfully, False otherwise
    """
    # SMTP Configuration (from msmtp config)
    smtp_server = "smtppro.zoho.com"
    smtp_port = 587
    smtp_user = "<EMAIL>"
    sender_email = "<EMAIL>"

    # Get password from .env file
    if not smtp_password:
        encrypted_password = os.environ.get("ZOHO_SMTP_PASSWORD")
        if not encrypted_password:
            # print("Warning: ZOHO_SMTP_PASSWORD not found in environment variables")
            # Fallback to getpass if not found, though this might not be ideal if it's always encrypted
            smtp_password = getpass("Enter SMTP <NAME_EMAIL>: ")
        else:
            try:
                # Assuming SECRET_KEY is also an environment variable
                secret_key = os.environ.get("SECRET_KEY")
                if not secret_key:
                    # print("Error: SECRET_KEY not found in environment variables for decryption.")
                    return False
                # print(f"DEBUG: Using SECRET_KEY for decryption (first 5 chars): {secret_key[:5]}...") # Temporary debug line
                smtp_password = decrypt_id(secret_key, encrypted_password)
                # print(f"DEBUG: Decrypted SMTP password: {smtp_password}")  # Debug line to show decrypted password
            except Exception as e:
                # print(f"Failed to decrypt password: {e}")
                return False

    # Create a multipart message
    msg = MIMEMultipart()
    msg['From'] = sender_email
    msg['To'] = recipient
    msg['Subject'] = subject

    # Add message body as HTML
    msg.attach(MIMEText(message, 'html'))

    try:
        if debug:
            print(f"📧 [DEBUG] Attempting to send email to: {recipient}")
            print(f"📧 [DEBUG] SMTP Server: {smtp_server}:{smtp_port}")
            print(f"📧 [DEBUG] SMTP User: {smtp_user}")
            print(f"📧 [DEBUG] Subject: {subject}")

        # Create SMTP session
        server = smtplib.SMTP(smtp_server, smtp_port)
        if debug:
            server.set_debuglevel(1)  # Enable detailed SMTP debugging when debug=True

        server.ehlo()  # Identify ourselves to the server
        if debug:
            print("📧 [DEBUG] EHLO successful")

        server.starttls()  # Secure the connection
        if debug:
            print("📧 [DEBUG] STARTTLS successful")

        server.ehlo()  # Re-identify ourselves over TLS connection
        if debug:
            print("📧 [DEBUG] Second EHLO successful")

        # Login to server
        if debug:
            print(f"📧 [DEBUG] Attempting login with user: {smtp_user}")
        server.login(smtp_user, smtp_password)
        if debug:
            print("📧 [DEBUG] Login successful")

        # Send email
        if debug:
            print("📧 [DEBUG] Sending email...")
        server.send_message(msg)
        if debug:
            print("📧 [DEBUG] Email sent successfully")

        # Terminate the session
        server.quit()
        if debug:
            print("📧 [DEBUG] SMTP session terminated")

        return True

    except Exception as e:
        print(f"❌ Failed to send email to {recipient}: {e}")
        print(f"❌ Error type: {type(e).__name__}")
        import traceback
        print(f"❌ Full traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    # Example usage
    recipient = "<EMAIL>"
    subject = "Test HTML Email from Python smtplib"
    message = """
    <html>
      <body>
        <h1>Hello,</h1>
        <p>This is a test HTML email sent using Python's <code>smtplib</code> module.</p>
        <p>This email confirms the successful migration from msmtp to smtplib and HTML email sending.</p>
        <p>Best regards,<br>Israeli Coffee Team</p>
      </body>
    </html>
    """

    # Print the password being used (for debugging only - remove in production)
    # For security, avoid printing the decrypted password.
    # If you need to confirm it's being loaded, print the encrypted one or a status message.
    encrypted_pass_from_env = os.environ.get('ZOHO_SMTP_PASSWORD')
    if encrypted_pass_from_env:
        print(f"Using encrypted password from .env: {encrypted_pass_from_env[:10]}...") # Print a portion for confirmation
    else:
        print("ZOHO_SMTP_PASSWORD not found in .env for testing.")

    send_email(recipient, subject, message)