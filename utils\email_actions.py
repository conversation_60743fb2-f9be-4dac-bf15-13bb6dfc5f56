#!/usr/bin/env python3
"""
Email action utilities for handling email verification and password reset tokens.
This module provides secure token generation and verification for email-based actions.
"""

import os
import time
from itsdangerous import URLSafeTimedSerializer, SignatureExpired, BadSignature


class EmailActionError(Exception):
    """Custom exception for email action errors"""
    pass


def get_email_serializer(secret_key=None, salt="email-actions"):
    """
    Get a URLSafeTimedSerializer for email actions.

    Args:
        secret_key (str, optional): Secret key for signing. Uses FLASK_SECRET_KEY if not provided.
        salt (str): Salt for the serializer

    Returns:
        URLSafeTimedSerializer: Configured serializer
    """
    if not secret_key:
        secret_key = os.environ.get('FLASK_SECRET_KEY')
        if not secret_key:
            raise EmailActionError("No secret key available for email token generation")

    return URLSafeTimedSerializer(secret_key, salt=salt)


def generate_email_verification_token(email, uid):
    """
    Generate a secure token for email verification.

    Args:
        email (str): User's email address
        uid (str): User's Firebase UID

    Returns:
        str: Secure token for email verification
    """
    serializer = get_email_serializer(salt="email-verification")

    # Create payload with email, uid, and timestamp
    payload = {
        'email': email,
        'uid': uid,
        'action': 'verify_email',
        'timestamp': int(time.time())
    }

    return serializer.dumps(payload)


def generate_password_reset_token(email, uid):
    """
    Generate a secure token for password reset.

    Args:
        email (str): User's email address
        uid (str): User's Firebase UID

    Returns:
        str: Secure token for password reset
    """
    serializer = get_email_serializer(salt="password-reset")

    # Create payload with email, uid, and timestamp
    payload = {
        'email': email,
        'uid': uid,
        'action': 'reset_password',
        'timestamp': int(time.time())
    }

    return serializer.dumps(payload)


def verify_email_token(token, max_age=3600):
    """
    Verify an email verification token.

    Args:
        token (str): Token to verify
        max_age (int): Maximum age in seconds (default: 1 hour)

    Returns:
        dict: Payload if valid, None if invalid

    Raises:
        EmailActionError: If token is invalid or expired
    """
    serializer = get_email_serializer(salt="email-verification")

    try:
        payload = serializer.loads(token, max_age=max_age)

        # Validate payload structure
        required_fields = ['email', 'uid', 'action']
        if not all(field in payload for field in required_fields):
            raise EmailActionError("Invalid token payload")

        if payload.get('action') != 'verify_email':
            raise EmailActionError("Token not for email verification")

        return payload

    except SignatureExpired:
        raise EmailActionError("Verification link has expired")
    except BadSignature:
        raise EmailActionError("Invalid verification link")


def verify_password_reset_token(token, max_age=3600):
    """
    Verify a password reset token.

    Args:
        token (str): Token to verify
        max_age (int): Maximum age in seconds (default: 1 hour)

    Returns:
        dict: Payload if valid, None if invalid

    Raises:
        EmailActionError: If token is invalid or expired
    """
    serializer = get_email_serializer(salt="password-reset")

    try:
        payload = serializer.loads(token, max_age=max_age)

        # Validate payload structure
        required_fields = ['email', 'uid', 'action']
        if not all(field in payload for field in required_fields):
            raise EmailActionError("Invalid token payload")

        if payload.get('action') != 'reset_password':
            raise EmailActionError("Token not for password reset")

        return payload

    except SignatureExpired:
        raise EmailActionError("Password reset link has expired")
    except BadSignature:
        raise EmailActionError("Invalid password reset link")


def create_verification_email_html(verification_url, user_email):
    """
    Create HTML content for email verification email.

    Args:
        verification_url (str): URL for email verification
        user_email (str): User's email address

    Returns:
        str: HTML content for the email
    """
    return f"""
    <html>
      <head>
        <style>
          body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
          .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
          .header {{ background-color: #8B4513; color: white; padding: 20px; text-align: center; }}
          .content {{ padding: 20px; background-color: #f9f9f9; }}
          .button {{ display: inline-block; padding: 12px 24px; background-color: #8B4513; color: white; text-decoration: none; border-radius: 5px; margin: 20px 0; }}
          .footer {{ padding: 20px; text-align: center; font-size: 12px; color: #666; }}
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Israeli Coffee - Email Verification</h1>
          </div>
          <div class="content">
            <h2>Welcome to Israeli Coffee!</h2>
            <p>Thank you for registering with Israeli Coffee using the email address <strong>{user_email}</strong>. To complete your registration and start exploring the best coffee roasters in Israel, please verify your email address.</p>

            <p>Click the button below to verify your email address:</p>

            <a href="{verification_url}" class="button">Verify Email Address</a>

            <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
            <p><a href="{verification_url}">{verification_url}</a></p>

            <p>This verification link will expire in 1 hour for security reasons.</p>

            <p>If you didn't create an account with Israeli Coffee, you can safely ignore this email.</p>
          </div>
          <div class="footer">
            <p>Israeli Coffee - Connecting you with the best coffee roasters in Israel</p>
            <p>Email: <EMAIL></p>
          </div>
        </div>
      </body>
    </html>
    """


def create_password_reset_email_html(reset_url, user_email):
    """
    Create HTML content for password reset email.

    Args:
        reset_url (str): URL for password reset
        user_email (str): User's email address

    Returns:
        str: HTML content for the email
    """
    return f"""
    <html>
      <head>
        <style>
          body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
          .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
          .header {{ background-color: #8B4513; color: white; padding: 20px; text-align: center; }}
          .content {{ padding: 20px; background-color: #f9f9f9; }}
          .button {{ display: inline-block; padding: 12px 24px; background-color: #8B4513; color: white; text-decoration: none; border-radius: 5px; margin: 20px 0; }}
          .footer {{ padding: 20px; text-align: center; font-size: 12px; color: #666; }}
          .warning {{ background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; border-radius: 5px; margin: 10px 0; }}
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Israeli Coffee - Password Reset</h1>
          </div>
          <div class="content">
            <h2>Password Reset Request</h2>
            <p>We received a request to reset the password for your Israeli Coffee account ({user_email}).</p>

            <p>Click the button below to reset your password:</p>

            <a href="{reset_url}" class="button">Reset Password</a>

            <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
            <p><a href="{reset_url}">{reset_url}</a></p>

            <div class="warning">
              <strong>Security Notice:</strong>
              <ul>
                <li>This password reset link will expire in 1 hour</li>
                <li>If you didn't request this password reset, you can safely ignore this email</li>
                <li>Your password will not be changed unless you click the link above and complete the process</li>
              </ul>
            </div>
          </div>
          <div class="footer">
            <p>Israeli Coffee - Connecting you with the best coffee roasters in Israel</p>
            <p>Email: <EMAIL></p>
          </div>
        </div>
      </body>
    </html>
    """
