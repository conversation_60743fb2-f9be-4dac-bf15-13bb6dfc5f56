#!/usr/bin/env python3
"""
Test script to verify SMTP email sending is working correctly.
This script tests the email sending functionality using your Zoho SMTP configuration.
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the current directory to the Python path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from sending_mail import send_email
from utils.email_actions import create_verification_email_html, create_password_reset_email_html

def test_smtp_connection():
    """Test basic SMTP email sending"""
    print("Testing SMTP Email Sending...")
    print("=" * 50)
    
    # Check required environment variables
    required_vars = ['ZOHO_SMTP_PASSWORD', 'SECRET_KEY']
    missing_vars = []
    
    for var in required_vars:
        if not os.environ.get(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ Missing required environment variables: {', '.join(missing_vars)}")
        print("Please ensure these are set in your .env file")
        return False
    
    print("✅ Required environment variables found")
    
    # Test email details
    test_recipient = "<EMAIL>"  # Your test email
    test_subject = "Israeli Coffee - Backend Email Test"
    
    # Create a test email
    test_message = """
    <html>
      <head>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background-color: #8B4513; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background-color: #f9f9f9; }
          .success { color: #28a745; font-weight: bold; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Israeli Coffee - Email System Test</h1>
          </div>
          <div class="content">
            <h2>✅ Backend Email System Working!</h2>
            <p>This email confirms that your backend email system is working correctly.</p>
            
            <div class="success">
              <p>✅ SMTP Configuration: Working</p>
              <p>✅ Email Templates: Working</p>
              <p>✅ Backend Integration: Working</p>
            </div>
            
            <p>Your email verification and password reset emails will now be sent through your Zoho SMTP server instead of Firebase.</p>
            
            <p><strong>Next Steps:</strong></p>
            <ul>
              <li>Test user registration with email verification</li>
              <li>Test password reset functionality</li>
              <li>Update Firebase Console action URLs</li>
            </ul>
          </div>
        </div>
      </body>
    </html>
    """
    
    print(f"📧 Sending test email to: {test_recipient}")
    print("⏳ Please wait...")
    
    try:
        success = send_email(
            recipient=test_recipient,
            subject=test_subject,
            message=test_message
        )
        
        if success:
            print("✅ Test email sent successfully!")
            print(f"📬 Check your inbox at {test_recipient}")
            return True
        else:
            print("❌ Failed to send test email")
            print("Check your SMTP configuration and credentials")
            return False
            
    except Exception as e:
        print(f"❌ Error sending test email: {e}")
        return False

def test_email_templates():
    """Test email template generation"""
    print("\nTesting Email Templates...")
    print("=" * 50)
    
    try:
        # Test verification email template
        verification_url = "https://israeli.coffee/verify-email/test-token-123"
        user_email = "<EMAIL>"
        
        verification_html = create_verification_email_html(verification_url, user_email)
        print("✅ Email verification template generated")
        
        # Test password reset email template
        reset_url = "https://israeli.coffee/reset-password/test-token-456"
        reset_html = create_password_reset_email_html(reset_url, user_email)
        print("✅ Password reset template generated")
        
        # Check if templates contain expected content
        assert verification_url in verification_html
        assert user_email in verification_html
        assert "Israeli Coffee" in verification_html
        print("✅ Verification template content validated")
        
        assert reset_url in reset_html
        assert user_email in reset_html
        assert "Password Reset" in reset_html
        print("✅ Password reset template content validated")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing email templates: {e}")
        return False

def main():
    """Run all tests"""
    print("Israeli Coffee - Backend Email System Test")
    print("=" * 60)
    
    tests_passed = 0
    total_tests = 2
    
    # Test email templates
    if test_email_templates():
        tests_passed += 1
    
    # Test SMTP sending
    if test_smtp_connection():
        tests_passed += 1
    
    print("\n" + "=" * 60)
    print(f"Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! Your backend email system is ready.")
        print("\n📋 Next Steps:")
        print("1. Test user registration to verify email verification works")
        print("2. Test password reset functionality")
        print("3. Update Firebase Console action URLs when ready")
        return True
    else:
        print("⚠️  Some tests failed. Please check your configuration.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
