{% extends "base.html" %}
{% block title %}Israeli.Coffee{% endblock %}
{% block content %}
<!DOCTYPE html>
<html lang="he" dir="rtl">
<head>
  <meta charset="UTF-8">
  <title>Database Populator</title>
  <style>
    body {
      font-family: sans-serif;
      margin: 20px;
      direction: rtl;
      text-align: right;
      background: url('/static/images/coffee_bg.webp');
      background-size: cover;
      background-position: center;
      background-repeat: no-repeat;
      background-attachment: fixed;
    }
    nav a { margin-right:10px; }
    @import url('https://fonts.googleapis.com/css2?family=Rammetto+One&display=swap');
  </style>
  <!-- New high contrast overrides -->
  <style>
    body.high-contrast {
      background: black !important;
      color: white !important;
      background-image: none !important;
    }
    body.high-contrast h1,
    body.high-contrast p,
    body.high-contrast a {
      color: white !important;
    }
    /* Ensure buttons and nav links get high contrast styling */
    body.high-contrast .custom-btn,
    body.high-contrast button {
      background: black !important;
      color: white !important;
      border: 1px solid white !important;
    }
  </style>
</head>
<body>
  
  <!-- New title inserted just below the header -->
<div class="text-center mt-4 md:mt-6">
  <h1 class="font-rammetto font-normal m-0 text-4xl sm:text-5xl md:text-7xl lg:text-8xl text-white">Israeli.Coffee</h1>
</div>
  <!-- Insert the two RTL H1 titles -->
  <div class="mt-8 md:mt-16 text-center px-4">
    <h1 class="text-xl sm:text-2xl md:text-4xl lg:text-5xl text-white font-varela mb-4 sm:mb-4 leading-relaxed">עולם הקפה הישראלי – לחובבי הקפה</h1>
    <h1 class="text-xl sm:text-2xl md:text-4xl lg:text-5xl text-white font-varela leading-relaxed">מצאו את פולי הקפה שאתם אוהבים, בעשרות בתי קלייה</h1>
  </div>

  <!-- Responsive button grid -->
  <div class="flex flex-wrap justify-center items-center gap-4 mt-6 md:mt-10 px-4">
    <style>
      /* Base styles for custom buttons */
      .custom-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 150px;
        height: 150px;
        text-decoration: none;
        border-radius: 15px;
        color: white;
        text-align: center;
        overflow: hidden;
        white-space: normal;
        font-size: 150%;
        cursor: pointer;
        min-width: 44px;
        min-height: 44px;
        touch-action: manipulation;
      }

      /* Responsive button sizes */
      @media (min-width: 640px) {
        .custom-btn {
          width: 180px;
          height: 180px;
          font-size: 175%;
        }
      }

      @media (min-width: 768px) {
        .custom-btn {
          width: 200px;
          height: 200px;
          font-size: 200%;
        }
      }

      .btn-blue {
        background-color: blue;
        margin-left: 10px;
      }

      .btn-green {
        background-color: green;
        margin-left: 10px;
      }

      .btn-orange {
        background-color: orange;
        margin-left: 10px;
      }
      .btn-pink {
        background-color: rgb(244, 75, 137);
        margin-left: 10px;
      }
      .btn-purple {
        background-color: purple; /* Or any other bright color you prefer, e.g., #8A2BE2 (blueviolet) */
        margin-left: 10px;
      }
      /* Only apply hover effects on non-touch devices */
      @media (hover: hover) {
        .btn-blue:hover {
          background-color: navy;
        }

        .btn-green:hover {
          background-color: darkgreen;
        }

        .btn-orange:hover {
          background-color: darkorange;
        }
        .btn-pink:hover {
          background-color: rgb(241, 41, 114);
        }
        .btn-purple:hover {
          background-color: darkmagenta; /* Darker shade for purple */
        }
      }

      /* Style for login modal */
      #loginPromptModal {
        display: none;
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0,0,0,0.4);
      }

      .modal-content {
        background-color: #fefefe;
        margin: 15% auto;
        padding: 20px;
        border: 1px solid #888;
        width: 90%;
        max-width: 300px;
        text-align: center;
        border-radius: 5px;
      }

      .modal-btn {
        margin: 10px;
        padding: 12px 20px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        min-width: 44px;
        min-height: 44px;
        touch-action: manipulation;
      }

      .login-btn {
        background-color: #4285f4;
        color: white;
      }

      .cancel-btn {
        background-color: #f1f1f1;
      }
    </style>

    <!-- Updated buttons - conditional behavior based on login status -->
    {% if g.user %}
      <a href="{{ url_for('search_beans') }}" class="custom-btn btn-blue">חיפוש<br>פולי קפה</a>
      <a href="{{ url_for('show_brew') }}" class="custom-btn btn-green">החליטות שלי</a>
    {% else %}
      <button class="custom-btn btn-blue" onclick="showLoginPrompt('search_beans')" disabled>חיפוש<br>פולי קפה</button>
      <button class="custom-btn btn-green" onclick="showLoginPrompt('show_brew')" disabled>החליטות שלי</button>
    {% endif %}

    {% if session.get('roaster_user') %}
    <!-- Only show this button if the user is a roaster -->
    <a href="{{ url_for('manage_roaster') }}" class="custom-btn btn-orange">ניהול<br>בית קלייה</a>
    {% if g.is_admin %}
    <a href="{{ url_for('add_roaster') }}" class="custom-btn btn-purple">הוסף<br>בית קלייה</a>
    <a href="{{ url_for('link_roaster')}}" class="custom-btn btn-pink">קישור<br>בית קלייה</a>
    {% endif %}
  </div>
  {% endif %}

  <!-- Login prompt modal -->
  <div id="loginPromptModal" class="modal flex items-center justify-center">
    <div class="modal-content">
      <h2 class="text-xl font-bold mb-2">התחברות נדרשת</h2>
      <p class="mb-4">יש להתחבר כדי לגשת לדף זה</p>
      <div class="flex justify-center">
        <button class="modal-btn login-btn text-lg" onclick="redirectToLogin()">התחבר</button>
        <button class="modal-btn cancel-btn text-lg" onclick="closeLoginPrompt()">ביטול</button>
      </div>
    </div>
  </div>
  
  <!-- Fix the JavaScript section that has errors -->
  <script>
    // Set flag for roaster user using session variables passed from server
    var roaster_user = "{{ 'true' if session.get('roaster_user') else 'false' }}";
    var roaster_id = "{{ session.get('roaster_id', '') }}";
    var targetPage = "";
    
    console.log("Roaster user flag:", roaster_user, "Roaster ID:", roaster_id);
    
    // If user is a roaster, we can display additional functionality
    if (roaster_user) {
      document.addEventListener('DOMContentLoaded', function() {
        console.log("User is a roaster! Additional roaster features enabled.");
      });
    }
    
    // Functions for login modal
    function showLoginPrompt(page) {
      targetPage = page;
      document.getElementById('loginPromptModal').style.display = 'block';
    }
    
    function closeLoginPrompt() {
      document.getElementById('loginPromptModal').style.display = 'none';
    }
    
    function redirectToLogin() {
      window.location.href = "{{ url_for('login') }}?next=" + targetPage;
    }
    
    // Close modal when clicking outside
    window.onclick = function(event) {
      var modal = document.getElementById('loginPromptModal');
      if (event.target == modal) {
        closeLoginPrompt();
      }
    }
  </script>

  <!-- Add footer with links -->
  <footer class="fixed bottom-0 w-full text-center py-2 text-sm" dir="rtl">
    <div class="container mx-auto">
      <a href="#" class="text-white hover:text-gray-300 mx-2" id="about-link">אודות</a> |
      <a href="#" class="text-white hover:text-gray-300 mx-2" id="terms-link">תנאי שימוש</a> |
      <a href="#" class="text-white hover:text-gray-300 mx-2" id="contact-link">צור קשר</a>
    </div>
  </footer>

  <!-- Modal Templates -->
  <div id="about-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center">
    <div class="bg-white rounded-lg p-6 overflow-y-auto" dir="rtl" style="width: 95%; height: 80%; max-width: 1200px;">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-bold">אודות</h2>
        <button class="close-modal text-gray-500 hover:text-gray-800 text-2xl">&times;</button>
      </div>
      <div style="width: 100%;">
        {% include 'modals/about.html' %}
      </div>
    </div>
  </div>

  <div id="terms-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center">
    <div class="bg-white rounded-lg p-6 oclso" dir="rtl" style="width: 95%; height: 80%; max-width: 1200px;">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-bold">תנאי שימוש והגבלות</h2>
        <button class="close-modal text-gray-500 hover:text-gray-800 text-2xl">&times;</button>
      </div>
      <div style="width: 100%;">
        {% include 'modals/terms.html' %}
      </div>
    </div>
  </div>

  <div id="contact-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center">
    <div class="bg-white rounded-lg p-6 overflow-y-auto" dir="rtl" style="width: 95%; height: 80%; max-width: 1200px;">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-bold">צור קשר</h2>
        <button class="close-modal text-gray-500 hover:text-gray-800 text-2xl">&times;</button>
      </div>
      <div style="width: 100%;">
        {% include 'modals/contact.html' %}
      </div>
    </div>
  </div>

  <!-- Add custom style to override any conflicting modal content styling -->
  <style>
    /* Override any conflicting modal styles */
    #about-modal .modal-content,
    #terms-modal .modal-content,
    #contact-modal .modal-content {
      width: 100% !important;
      max-width: none !important;
      margin: 0 !important;
      background-color: transparent !important;
      border: none !important;
      padding: 0 !important;
      box-sizing: border-box !important;
      text-align: right !important;
    }
    
    /* Ensure modals take the full available width */
    #about-modal, #terms-modal, #contact-modal {
      width: 100% !important;
    }
    
    /* Ensure form controls inside contact form are full width */
    #contact-modal input,
    #contact-modal textarea {
      width: 100% !important;
      box-sizing: border-box !important;
    }
  </style>

  <!-- Footer Modal Script -->
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Modal open functions
      document.getElementById('about-link').addEventListener('click', function(e) {
        e.preventDefault();
        document.getElementById('about-modal').style.display = 'flex';
      });
      
      document.getElementById('terms-link').addEventListener('click', function(e) {
        e.preventDefault();
        document.getElementById('terms-modal').style.display = 'flex';
      });
      
      document.getElementById('contact-link').addEventListener('click', function(e) {
        e.preventDefault();
        document.getElementById('contact-modal').style.display = 'flex';
      });
      
      // Close modal when clicking the X button
      document.querySelectorAll('.close-modal').forEach(function(btn) {
        btn.addEventListener('click', function() {
          this.closest('.fixed').style.display = 'none';
        });
      });
      
      // Close modal when clicking outside the content
      document.querySelectorAll('#about-modal, #terms-modal, #contact-modal').forEach(function(modal) {
        modal.addEventListener('click', function(e) {
          if (e.target === this) {
            this.style.display = 'none';
          }
        });
      });
      
      // Close modal with ESC key
      document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
          document.querySelectorAll('#about-modal, #terms-modal, #contact-modal').forEach(function(modal) {
            modal.style.display = 'none';
          });
        }
      });
    });
  </script>

</body>
</html>
{% endblock %}