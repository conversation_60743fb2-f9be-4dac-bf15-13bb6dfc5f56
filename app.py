from flask import Flask, render_template, request, redirect, url_for, jsonify, session, g, flash, current_app # Add flash and current_app
from urllib.parse import urlparse, urljoin
import sqlite3
import os
import firebase_admin
from firebase_admin import credentials, auth
import firebase_admin._token_gen  # Add this import for ExpiredIdTokenError
from functools import wraps
from datetime import datetime, date # Add this import at the top if not already present
import time
from dotenv import load_dotenv
from utils.db_utils import delete_brewlog_record, delete_brewlog_for_user
from utils.log_utils import setup_activity_logger # For activity logging
from flask_wtf.csrf import CSRFProtect, generate_csrf # Import CSRFProtect
from flask_wtf import FlaskForm # Import FlaskForm
from itsdangerous import URLSafeSerializer  # NEW import
from utils.encryption_utils import encrypt_id, decrypt_id
from sending_mail import send_email # Import the send_email function
from flask import send_from_directory
import csv
from utils.email_actions import generate_email_verification_token, generate_password_reset_token, verify_email_token, verify_password_reset_token

# Load environment variables from .env file
load_dotenv()

app = Flask(__name__)
# Load the secret key from an environment variable
app.secret_key = os.environ.get('FLASK_SECRET_KEY', 'a_default_secret_key_for_development') # Provide a default for development if the env var isn't set
csrf = CSRFProtect(app) # Initialize CSRF protection
# Create a serializer using the secret key and a fixed salt
serializer = URLSafeSerializer(app.secret_key, salt="roaster-salt")  # NEW

# Initialize Activity Logger
# The log path can be configured via an environment variable ACTIVITY_LOG_PATH
activity_log_file_path = os.environ.get('ACTIVITY_LOG_PATH', 'logs/activity.log')
activity_logger = setup_activity_logger(log_file_path=activity_log_file_path)

# Add security headers to improve application security
@app.after_request
def add_security_headers(response):
    """Add security headers to all responses"""
    # Phase 1: Basic security headers (minimal risk)

    # Prevent browsers from interpreting files as a different MIME type
    # This is very safe and rarely causes issues
    response.headers['X-Content-Type-Options'] = 'nosniff'

    # Safer referrer policy that doesn't break functionality
    # This controls how much referrer information is sent when navigating away from a page
    response.headers['Referrer-Policy'] = 'strict-origin-when-cross-origin'

    # Prevent your page from being displayed in an iframe on another domain
    # This prevents clickjacking attacks
    response.headers['X-Frame-Options'] = 'SAMEORIGIN'

    # Enable the browser's built-in XSS filter
    # This provides an additional layer of protection against cross-site scripting attacks
    response.headers['X-XSS-Protection'] = '1; mode=block'

    # Phase 2: Medium-risk headers

    # Content Security Policy in Enforcement mode
    # This enforces the policy and blocks any violations
    # Policy has been tested and adjusted based on application needs
    csp = "default-src 'self'; " + \
        "script-src 'self' 'unsafe-inline' https://www.gstatic.com https://*.firebaseio.com https://*.googleapis.com " + \
        "https://apis.google.com https://code.jquery.com https://*.jquery.com https://cdnjs.cloudflare.com https://cdn.tailwindcss.com; " + \
        "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdnjs.cloudflare.com " + \
        "https://code.jquery.com https://*.jquery.com https://cdn.tailwindcss.com; " + \
        "img-src 'self' data: https:; " + \
        "connect-src 'self' https://*.firebaseio.com https://*.googleapis.com; " + \
        "font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com; " + \
        "object-src 'none'; " + \
        "media-src 'self'; " + \
        "frame-src 'self' https://*.firebaseapp.com https://*.googleapis.com https://apis.google.com;"
    response.headers['Content-Security-Policy'] = csp

    # Restrict which browser features can be used
    # This prevents abuse of sensitive browser features
    response.headers['Permissions-Policy'] = 'camera=(), microphone=(), geolocation=()'

    return response

# @app.after_request
# def log_request_activity(response):
#     """Log incoming request activity after the request has been processed."""
#     try:
#         user_id = 'anonymous'
#         if hasattr(g, 'user') and g.user and hasattr(g.user, 'uid'):
#             user_id = g.user.uid
#         elif 'uid' in session:
#             user_id = session['uid']
#
#         log_data = {
#             "user_id": user_id,
#             "action": f"visited {request.path}",
#             "method": request.method,
#             "path": request.path,
#             "status_code": response.status_code,
#             "ip_address": request.remote_addr,
#             "user_agent": request.user_agent.string if request.user_agent else "Unknown"
#         }
#         # The timestamp is added by the JsonFormatter in log_utils
#         activity_logger.info("Page visit", extra={'custom_data': log_data})
#     except Exception as e:
#         # Log errors related to activity logging to the standard Flask logger
#         app.logger.error(f"Error in activity logging: {e}", exc_info=True)
#     return response

# Initialize Firebase Admin SDK
# Try to load from environment variables first, fall back to file if not available
try:
    # Check if all required Firebase Admin SDK environment variables are set
    firebase_env_vars = {
        'type': os.environ.get('FIREBASE_ADMIN_TYPE'),
        'project_id': os.environ.get('FIREBASE_ADMIN_PROJECT_ID'),
        'private_key_id': os.environ.get('FIREBASE_ADMIN_PRIVATE_KEY_ID'),
        'private_key': os.environ.get('FIREBASE_ADMIN_PRIVATE_KEY', '').replace('\\n', '\n'),
        'client_email': os.environ.get('FIREBASE_ADMIN_CLIENT_EMAIL'),
        'client_id': os.environ.get('FIREBASE_ADMIN_CLIENT_ID'),
        'auth_uri': os.environ.get('FIREBASE_ADMIN_AUTH_URI'),
        'token_uri': os.environ.get('FIREBASE_ADMIN_TOKEN_URI'),
        'auth_provider_x509_cert_url': os.environ.get('FIREBASE_ADMIN_AUTH_PROVIDER_X509_CERT_URL'),
        'client_x509_cert_url': os.environ.get('FIREBASE_ADMIN_CLIENT_X509_CERT_URL')
    }

    # Check if all required variables are present
    if all(firebase_env_vars.values()):
        # Use environment variables
        cred = credentials.Certificate(firebase_env_vars)
    else:
        # Fall back to service account file
        service_account_path = os.environ.get('FIREBASE_SERVICE_ACCOUNT_PATH', 'instance/serviceAccountKey.json')
        cred = credentials.Certificate(service_account_path)

    firebase_admin.initialize_app(cred)
except Exception as e:
    app.logger.critical(f"CRITICAL: Error initializing Firebase Admin SDK: {e}", exc_info=True)
    # In production, you might want to handle this more gracefully
    # For now, we'll re-raise to prevent the app from starting with broken authentication
    raise

# Add before_request handler to load user info if session token exists
@app.before_request
def load_logged_in_user():
    g.user = None  # Initialize g.user
    g.is_admin = False # Initialize g.is_admin

    if 'id_token' in session:
        try:
            token_details = auth.verify_id_token(session['id_token'], clock_skew_seconds=60)
            g.user = auth.get_user(token_details['uid'])

            if g.user:
                # Set session start time if not already set
                if 'session_start_time' not in session:
                    session['session_start_time'] = time.time()

                # Check if user is admin
                admin_uid_env = os.environ.get('ADMIN_UIDS')
                if admin_uid_env:
                    admin_uids = [uid.strip() for uid in admin_uid_env.split(',')]
                    if g.user.uid in admin_uids:
                        g.is_admin = True
                # No else needed for g.is_admin as it's initialized to False

                # Check if user is a roaster
                conn = get_db_connection()
                cursor = conn.cursor()
                cursor.execute("SELECT roaster_id FROM roasters WHERE uid = ?", (g.user.uid,))
                roaster_record = cursor.fetchone()
                conn.close()

                if roaster_record:
                    session['roaster_user'] = True
                    session['roaster_id'] = roaster_record['roaster_id']
                else:
                    session['roaster_user'] = False
                    session.pop('roaster_id', None)
        except firebase_admin._token_gen.ExpiredIdTokenError as e:
            app.logger.warning(f"Expired ID token: {e}")
            g.user = None
            g.is_admin = False
            session.clear()
            # Optionally, you can flash a message here if you want to notify the user
        except Exception as e:
            app.logger.error(f"Error loading user: {e}", exc_info=True)
            g.user = None
            g.is_admin = False
            session['roaster_user'] = False
            session.pop('roaster_id', None)
            session.pop('session_start_time', None)
    else:
        # Ensure g.user and g.is_admin are reset if no id_token
        g.user = None
        g.is_admin = False
        session['roaster_user'] = False
        session.pop('roaster_id', None)
        session.pop('session_start_time', None)

# Helper function to check for safe URLs (should be at module level)
def is_safe_url(target):
    ref_url = urlparse(request.host_url)
    test_url = urlparse(urljoin(request.host_url, target))
    return test_url.scheme in ('http', 'https') and \
           ref_url.netloc == test_url.netloc

DATABASE = 'coffee_database.db' # Should be at module level

# Decorator for login required
def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'id_token' not in session:
            app.logger.warning("No id_token in session, redirecting to home")
            flash("Please log in to access this page.", "warning")
            return redirect(url_for('home'))

        try:
            # Verify token and load full user record
            token_details = auth.verify_id_token(session['id_token'], check_revoked=True, clock_skew_seconds=60)
            g.user = auth.get_user(token_details['uid'])

            # ---> START EMAIL VERIFICATION CHECK
            if not g.user.email_verified:
                # Allow access only to the verification notice page or logout
                allowed_endpoints = ['verify_email_notice', 'logout', 'static', 'get_firebase_config', 'check_session']
                if request.endpoint not in allowed_endpoints:
                    flash("Please verify your email address before proceeding.", "info")
                    return redirect(url_for('verify_email_notice'))
            # ---> END EMAIL VERIFICATION CHECK

        except auth.RevokedIdTokenError:
            flash("Your session has been revoked. Please log in again.", "warning")
            session.clear()
            return redirect(url_for('home'))
        except auth.UserDisabledError:
            flash("Your account has been disabled.", "danger")
            session.clear()
            return redirect(url_for('home'))
        except Exception as e:
            app.logger.error(f"Error verifying token or checking email verification: {e}", exc_info=True) # Changed to app.logger.error
            flash("Your session is invalid or expired. Please log in again.", "warning")
            session.clear()
            return redirect(url_for('home'))

        return f(*args, **kwargs)
    return decorated_function

# Decorator for admin required
def admin_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # First, ensure user is logged in
        if not g.user:
            flash("Please log in to access this page.", "warning")
            return redirect(url_for('login', next=request.url))

        admin_uid_env = os.environ.get('ADMIN_UIDS')
        if not admin_uid_env:
            app.logger.error("@admin_required: ADMIN_UIDS environment variable is not set.")
            flash("Administrative access is misconfigured.", "danger")
            return redirect(url_for('home'))

        admin_uids = [uid.strip() for uid in admin_uid_env.split(',')]

        if g.user.uid not in admin_uids:
            flash("You do not have permission to access this page.", "danger")
            return redirect(url_for('home'))

        return f(*args, **kwargs)
    return decorated_function

@app.route('/login')
def login():
    # Check if the request is coming from our site
    referrer = request.referrer

    # If no referrer or not from our site, redirect to home
    if not referrer or not referrer.startswith(request.host_url):
        return redirect(url_for('home'))

    # Check if referrer is from an allowed page (index or other main pages)
    allowed_paths = ['/', '/index', '/index.html', '/search_beans', '/show_brew']
    referrer_path = referrer.replace(request.host_url, '').split('?')[0].rstrip('/')

    if referrer_path not in allowed_paths and not referrer_path.startswith('search_beans'):
        return redirect(url_for('home'))

    next_page = request.args.get('next')
    if next_page and not is_safe_url(next_page):
        app.logger.warning(f"Unsafe next_page URL detected and discarded: {next_page}")
        next_page = None # Discard unsafe URL
    if not next_page: # Default to home if no next_page or if it was unsafe
        next_page = url_for('home')

    return render_template('login.html', next=next_page)

@app.route('/register')
def register():
    # Check if the request is coming from our site
    referrer = request.referrer

    # If no referrer or not from our site, redirect to home
    if not referrer or not referrer.startswith(request.host_url):
        return redirect(url_for('home'))

    # Allow access from any page on our site
    # The referrer check above already ensures it's from our domain
    return render_template('register.html')

@app.route('/login_modal')
@app.route('/login_modal.html')
def login_modal():
    # Prevent direct access to login_modal.html
    # Always redirect to home page
    return redirect(url_for('home'))

@app.route('/set_token', methods=['POST'])
@csrf.exempt  # Exempt from CSRF protection since this is called during registration
def set_token():
    try:
        # Log the incoming request for debugging
        app.logger.info(f"🔧 set_token called - Content-Type: {request.content_type}")

        # Check if request has JSON data
        if not request.is_json:
            app.logger.error(f"❌ set_token: Request is not JSON. Content-Type: {request.content_type}")
            return jsonify({'message': 'Request must be JSON'}), 400

        data = request.get_json()
        if not data:
            app.logger.error("❌ set_token: No JSON data received")
            return jsonify({'message': 'No JSON data provided'}), 400

        token = data.get('token')
        allow_unverified = data.get('allow_unverified', False)

        app.logger.info(f"🔧 set_token: token length={len(token) if token else 0}, allow_unverified={allow_unverified}")

        if not token:
            app.logger.error("❌ set_token: No token provided in request")
            return jsonify({'message': 'No token provided'}), 400

        # Verify the token
        app.logger.info("🔧 set_token: Verifying Firebase token...")
        decoded_token = auth.verify_id_token(token, clock_skew_seconds=60)
        user = auth.get_user(decoded_token['uid'])

        app.logger.info(f"🔧 set_token: Token verified for user {user.email} (verified: {user.email_verified})")

        # Check email verification unless explicitly allowed (for registration flow)
        if not user.email_verified and not allow_unverified:
            app.logger.info(f"❌ set_token: Email not verified for {user.email} and allow_unverified=False")
            return jsonify({'message': 'Email not verified. Please check your inbox and verify your email before logging in.'}), 401

        # Set session data
        session['id_token'] = token
        session['uid'] = decoded_token['uid']
        session['session_start_time'] = time.time()

        # Log successful token setting
        app.logger.info(f"✅ Token set successfully for user: {user.email} (verified: {user.email_verified})")

        return jsonify({'message': 'Token set successfully'}), 200

    except auth.InvalidIdTokenError as e:
        app.logger.error(f"❌ set_token: Invalid Firebase token: {e}")
        return jsonify({'message': 'Invalid Firebase token'}), 401
    except auth.ExpiredIdTokenError as e:
        app.logger.error(f"❌ set_token: Expired Firebase token: {e}")
        return jsonify({'message': 'Expired Firebase token'}), 401
    except Exception as e:
        app.logger.error(f"❌ set_token: Unexpected error: {e}", exc_info=True)
        return jsonify({'message': f'Server error: {str(e)}'}), 500

@app.route('/reset_password', methods=['POST'])
def reset_password():
    """Send a password reset email to the user via backend"""
    data = request.get_json()
    email = data.get('email')

    if not email:
        return jsonify({'message': 'No email provided'}), 400

    try:
        # Check if the user exists first
        try:
            user = auth.get_user_by_email(email)
            app.logger.info(f"Sending password reset email to existing user: {email}")

            # Generate password reset token
            token = generate_password_reset_token(user.email, user.uid)

            # Create reset URL
            reset_url = url_for('reset_password_form', token=token, _external=True)

            # Create email content
            from utils.email_actions import create_password_reset_email_html
            email_html = create_password_reset_email_html(reset_url, user.email)

            # Send email
            email_sent = send_email(
                recipient=user.email,
                subject="Israeli Coffee - Password Reset Request",
                message=email_html
            )

            if email_sent:
                app.logger.info(f"Password reset email sent successfully to {email}")
            else:
                app.logger.error(f"Failed to send password reset email to {email}")

        except auth.UserNotFoundError:
            # User doesn't exist, but we'll still return success to prevent email enumeration
            app.logger.info(f"Password reset requested for non-existent email: {email}")

        # Always return success to prevent email enumeration
        return jsonify({'message': 'אם האימייל רשום במערכת, נשלח אליך קישור לאיפוס סיסמה'}), 200

    except Exception as e:
        app.logger.error(f"Error in password reset process: {e}", exc_info=True)
        # Return a generic message to avoid revealing if the email exists
        return jsonify({'message': 'אם האימייל רשום במערכת, נשלח אליך קישור לאיפוס סיסמה'}), 200

@app.route('/logout')
def logout():
    try:
        user_id = session.get('uid', 'anonymous')
        start_time = session.get('session_start_time')

        if start_time:
            duration_seconds = time.time() - start_time
            log_data = {
                "user_id": user_id,
                "session_duration_seconds": round(duration_seconds, 2)
            }
            # The JsonFormatter will add timestamp and level.
            # The message "User session ended" will be part of the log.
            activity_logger.info("User session ended", extra={'custom_data': log_data})
    except Exception as e:
        app.logger.error(f"Error during logout activity logging: {e}", exc_info=True)
    finally:
        session.clear()  # Clear all session data
    return redirect(url_for('home'))

def get_db_connection():
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    return conn

@app.route('/', defaults={'path': ''})
@app.route('/<path:path>')
def home(path):
    # Check for the special bypass path
    bypass_path = 'kdjHJFUIKJ-KJkdjKJKFweE'  # Do not use # in URLs
    # Remove any trailing slashes for comparison
    normalized_path = path.rstrip('/')
    # Check WebpageON environment variable
    webpage_on = os.environ.get('WebpageON', '1')
    # If user is logged in, always show index.html
    if getattr(g, 'user', None):
        return render_template('index.html')
    # If WebpageON is 1 or the special bypass path is used, show index.html
    if webpage_on == '1' or normalized_path == bypass_path:
        return render_template('index.html')
    # Otherwise, show under construction page
    return render_template('index_construction.html')

@app.route('/add_roaster', methods=['GET', 'POST'])
@admin_required # Changed from @login_required
def add_roaster():
    # Referrer checks can be kept as an additional layer if desired, or removed if @admin_required is sufficient.
    # For simplicity, I'm keeping them for now.
    referrer = request.referrer
    if not referrer or not referrer.startswith(request.host_url):
        return redirect(url_for('home'))
    allowed_paths = ['/', '/index', '/index.html', '/manage_roaster', '/link_roaster'] # Added /link_roaster if admin navigates from there
    referrer_path = referrer.replace(request.host_url, '').split('?')[0].rstrip('/') or '/'
    if referrer_path not in allowed_paths:
        return redirect(url_for('home'))

    if request.method == 'POST':
        # Admin is adding a roaster. Roaster is not linked to a UID at this stage.
        # UID will be linked via /link_roaster by an admin.
        name = request.form['name']
        address = request.form['address']
        city = request.form['city']
        zip_code = request.form['zip'] # Renamed to avoid conflict with zip()
        email = request.form['email']
        webpage = request.form['webpage']
        minimun_shipping = request.form['minimun_shipping']
        shipping_cost = request.form['shipping_cost']

        # TODO: Add comprehensive input validation here for all fields

        conn = get_db_connection()
        cursor = conn.cursor()
        try:
            # Insert roaster without UID. UID will be added via /link_roaster.
            # Ensure your 'roasters' table schema allows 'uid' to be NULL or has a default.
            cursor.execute('''
                INSERT INTO roasters (name, address, city, zip, email, webpage, minimun_shipping, shipping_cost)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (name, address, city, zip_code, email, webpage, minimun_shipping, shipping_cost))
            new_roaster_id = cursor.lastrowid
            conn.commit()
            flash(f"Roaster '{name}' created successfully with ID {new_roaster_id}. You can now link it to a user.", "success")
            return redirect(url_for('link_roaster')) # Redirect admin to link this new roaster
        except sqlite3.Error as e:
            conn.rollback()
            app.logger.error(f"Admin database error adding roaster: {e}")
            flash("Failed to create roaster profile due to a database error.", "danger")
        finally:
            if conn:
                conn.close()
        # If error, fall through to render the form again, or redirect to an admin dashboard
        return redirect(url_for('add_roaster')) # Or an admin dashboard

    return render_template('add_roaster.html')

@app.route('/add_bean', methods=['GET', 'POST'])
@login_required
def add_bean():
    # Use roaster_id from session if available
    roaster_id = session.get('current_roaster_id', '')
    is_roaster_user = False
    if roaster_id and session.get('roaster_user') and str(session.get('roaster_id')) == str(roaster_id):
        is_roaster_user = True
    elif session.get('roaster_user') and session.get('roaster_id'):
        roaster_id = session.get('roaster_id')
        is_roaster_user = True

    conn = get_db_connection()
    cursor = conn.cursor()

    # Load accessible roasters - if user is a roaster, only show their roaster
    if is_roaster_user:
        cursor.execute("SELECT roaster_id, name from roasters WHERE roaster_id = ?", (roaster_id,))
    else:
        cursor.execute("SELECT roaster_id, name from roasters")

    roasters = cursor.fetchall()
    cursor.execute("SELECT country FROM countries ORDER BY country ASC")
    countries = cursor.fetchall()
    cursor.execute("SELECT c_flavor FROM coffee_flavors ORDER BY c_flavor ASC")
    flavors = cursor.fetchall()
    conn.close()

    # Convert roaster_id to integer if present
    try:
        if roaster_id:
            roaster_id = int(roaster_id)
    except ValueError:
        roaster_id = ''

    default_arabica = request.args.get('arabica') or session.get('default_arabica', '100')
    default_weight = request.args.get('weight') or session.get('default_weight', '1000')
    default_mix = request.args.get('mix') or session.get('default_mix', 'True')

    if request.method == 'GET':
        session['default_arabica'] = default_arabica
        session['default_weight'] = default_weight
        session['default_mix'] = default_mix

    if request.method == 'POST':
        roaster_id = request.form['roaster_id']
        # Check if user is allowed to add beans for this roaster
        if session.get('roaster_user') and str(session.get('roaster_id')) != str(roaster_id):
            return "Unauthorized: You can only add beans for your own roaster", 403

        # ...existing bean adding code...
        bean_name = request.form['bean_name'].strip()
        if not bean_name:
            return "Bean name is required", 400
        origin = request.form['origin']  # Use the hidden input field 'origin'
        processing = request.form['processing']
        elevation = request.form['elevation']
        flavors = request.form['flavors']  # Use the hidden input field 'flavors'
        acidity = request.form['acidity']
        after_taste = request.form['after_taste']
        roast_level = request.form['roast_level']  # Use the hidden input field 'roast_level'
        body = request.form['body']  # Add the body field
        arabica = request.form['arabica'] if request.form['arabica'] else 0  # Set default value to zero if empty
        robusta = request.form['robusta'] if request.form['robusta'] else 0  # Set default value to zero if empty
        turkish = request.form.get('turkish', 'False') == 'True'
        espresso = request.form.get('espresso', 'False') == 'True'
        french_press = request.form.get('french_press', 'False') == 'True'
        pour_over = request.form.get('pour_over', 'False') == 'True'
        drip = request.form.get('drip', 'False') == 'True'
        cold_brew = request.form.get('cold_brew', 'False') == 'True'
        mix = request.form['mix'] == 'True'  # Handle the mix field as a select input
        price = request.form['price']
        weight = request.form['weight']
        image_file = request.form['image_file']
        SCA_score = request.form['SCA_score']
        Speciality = request.form.get('Speciality', 'False') == 'True'
        decaf = request.form.get('decaf', 'False') == 'True'

        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute('''
            INSERT INTO beans (
                roaster_id, bean_name, origin, processing, elevation, flavors, acidity, after_taste, roast_level, body, arabica,
                robusta, turkish, espresso, french_press, pour_over, drip, cold_brew, mix, price, weight, image_file, SCA_score, Speciality, decaf
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (roaster_id, bean_name, origin, processing, elevation, flavors, acidity, after_taste, roast_level, body, arabica,
              robusta, turkish, espresso, french_press, pour_over, drip, cold_brew, mix, price, weight, image_file, SCA_score, Speciality, decaf))
        conn.commit()
        conn.close()
        flash('פולי הקפה נשמרו בהצלחה', 'success') # Add flash message
        return redirect(url_for('manage_roaster')) # Redirect back to manage roaster page
    return render_template(
        'add_bean.html',
        roasters=roasters,
        countries=countries,
        flavors=flavors,
        roaster_id=roaster_id,
        arabica=default_arabica,
        weight=default_weight,
        mix=default_mix,
        is_roaster_user=is_roaster_user,
        roaster_user=session.get('roaster_user')
    )

@app.route('/add_country', methods=['POST'])
@login_required
def add_country():
    data = request.get_json()
    country = data['country']
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute('INSERT INTO countries (country) VALUES (?)', (country,))
    conn.commit()
    conn.close()
    return jsonify({'success': True})

@app.route('/add_flavor', methods=['POST'])
@login_required
def add_flavor():
    data = request.get_json()
    flavor = data['flavor']
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute('INSERT INTO coffee_flavors (c_flavor) VALUES (?)', (flavor,))
    conn.commit()
    conn.close()
    return jsonify({'success': True})

@app.route('/get_roasters_by_city', methods=['POST'])
def get_roasters_by_city():
    data = request.get_json()
    if not data or 'city' not in data:
        return jsonify({'error': 'City parameter is required'}), 400

    city = data['city']

    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT roaster_id, name FROM roasters WHERE city = ?", (city,))
        roasters_data = cursor.fetchall()
        conn.close()

        roasters = [{'id': str(row['roaster_id']), 'name': row['name']} for row in roasters_data]
        return jsonify({'roasters': roasters})
    except Exception as e:
        print(f"Error fetching roasters by city: {str(e)}")
        if 'conn' in locals():
            conn.close()
        return jsonify({'error': str(e)}), 500

@app.route('/update_beans', methods=['GET', 'POST'])
@login_required
def update_beans():
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute("SELECT roaster_id, name FROM roasters")
    roasters = cursor.fetchall()
    roaster_id = request.args.get('roaster_id')

    beans = []
    if request.method == 'POST':
        # ...existing code...
        list_all = request.form.get('list_all')
        roaster_id_post = request.form.get('roaster_id')
        if list_all:
            cursor.execute("SELECT * FROM beans")
        elif roaster_id_post:
            cursor.execute("SELECT * FROM beans WHERE roaster_id = ?", (roaster_id_post,))
        beans = cursor.fetchall()
    elif roaster_id:
        cursor.execute("SELECT * FROM beans WHERE roaster_id = ?", (roaster_id,))
        beans = cursor.fetchall()

    conn.close()
    return render_template('update_beans.html', roasters=roasters, beans=beans)

@app.route('/edit_bean', methods=['POST', 'GET'])
@login_required
def edit_bean():
    if request.method == 'POST':
        bean_id = request.form.get('bean_id')
        if not bean_id:
            return "Missing bean_id", 400
        session['current_bean_id'] = bean_id
    else:
        bean_id = session.get('current_bean_id')
        if not bean_id:
            return redirect(url_for('manage_roaster'))

    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute("SELECT * FROM beans WHERE bean_id = ?", (bean_id,))
    bean = cursor.fetchone()

    if not bean:
        conn.close()
        flash("Bean not found.", "danger")
        return redirect(url_for('manage_roaster'))

    # Authorization Check: Ensure the bean belongs to the logged-in roaster
    authenticated_roaster_id = session.get('roaster_id')
    if not session.get('roaster_user') or str(bean['roaster_id']) != str(authenticated_roaster_id):
        conn.close()
        flash("You are not authorized to edit this bean.", "danger")
        return redirect(url_for('manage_roaster'))

    cursor.execute("SELECT country FROM countries ORDER BY country ASC")
    countries = cursor.fetchall()
    cursor.execute("SELECT c_flavor FROM coffee_flavors ORDER BY c_flavor ASC")
    flavors = cursor.fetchall()
    # current_roaster_id is now confirmed to be the authenticated roaster's ID for this bean
    session['current_roaster_id'] = bean['roaster_id']
    conn.close()

    if request.method == 'POST' and 'bean_name' in request.form:
        # This is a form submission to update the bean
        bean_id = request.form.get('bean_id')
        if not bean_id:
            bean_id = session.get('current_bean_id')
            if not bean_id:
                return "Missing bean_id", 400

        bean_name = request.form['bean_name']
        origin = request.form['origin']
        processing = request.form['processing']
        elevation = request.form['elevation']
        flavors_input = request.form['flavors']
        acidity = request.form['acidity']
        after_taste = request.form['after_taste']
        roast_level = request.form['roast_level']
        body = request.form['body']
        arabica = request.form['arabica'] if request.form['arabica'] else 0
        robusta = request.form['robusta'] if request.form['robusta'] else 0
        mix = request.form['mix'] == 'True'
        price = request.form['price']
        weight = request.form['weight']
        image_file = request.form['image_file']
        turkish = request.form.get('turkish', 'False') == 'True'
        espresso = request.form.get('espresso', 'False') == 'True'
        french_press = request.form.get('french_press', 'False') == 'True'
        pour_over = request.form.get('pour_over', 'False') == 'True'
        drip = request.form.get('drip', 'False') == 'True'
        cold_brew = request.form.get('cold_brew', 'False') == 'True'
        SCA_score = request.form['SCA_score']
        Speciality = request.form.get('Speciality', 'False') == 'True'
        decaf = request.form.get('decaf', 'False') == 'True'

        # Ensure we use the authenticated roaster_id for the update operation
        auth_roaster_id_for_update = session.get('roaster_id')
        if not auth_roaster_id_for_update: # Should not happen due to @login_required and previous checks
            flash("Authentication error. Please log in again.", "danger")
            return redirect(url_for('manage_roaster'))

        conn = get_db_connection()
        cursor = conn.cursor()
        try:
            cursor.execute('''
                UPDATE beans SET
                    bean_name = ?, origin = ?, processing = ?, elevation = ?, flavors = ?, acidity = ?,
                    after_taste = ?, roast_level = ?, body = ?, arabica = ?, robusta = ?, mix = ?,
                    price = ?, weight = ?, image_file = ?, turkish = ?, espresso = ?, french_press = ?, pour_over = ?, drip = ?, cold_brew = ?, SCA_score = ?, Speciality = ?, decaf = ?
                WHERE bean_id = ? AND roaster_id = ?
            ''', (bean_name, origin, processing, elevation, flavors_input, acidity, after_taste, roast_level, body, arabica, robusta, mix,
                  price, weight, image_file, turkish, espresso, french_press, pour_over, drip, cold_brew, SCA_score, Speciality, decaf, bean_id, auth_roaster_id_for_update))

            if cursor.rowcount == 0:
                flash("Failed to update bean. It might have been deleted or you lack permission.", "warning")
            else:
                flash("Bean updated successfully.", "success")
            conn.commit()
        except sqlite3.Error as e:
            conn.rollback()
            app.logger.error(f"Database error updating bean {bean_id} for roaster {auth_roaster_id_for_update}: {e}")
            flash("An error occurred while updating the bean.", "danger")
        finally:
            conn.close()
        return redirect(url_for('manage_roaster'))

    return render_template('edit_bean.html', bean=bean, countries=countries, flavors=flavors)

@app.route('/delete_bean', methods=['POST'])
@login_required
def delete_bean():
    bean_id = request.form.get('bean_id')
    if not bean_id:
        flash("Bean ID is missing.", "danger")
        return redirect(url_for('manage_roaster'))

    if not session.get('roaster_user'):
        flash("You must be a roaster to delete beans.", "danger")
        return redirect(url_for('home')) # Or perhaps 'manage_roaster' if appropriate

    roaster_id = session.get('roaster_id')
    if not roaster_id:
        flash("Roaster ID not found in your session.", "danger")
        return redirect(url_for('home')) # Or 'manage_roaster'

    conn = get_db_connection()
    cursor = conn.cursor()
    try:
        # Ensure the bean belongs to the authenticated roaster before deleting
        cursor.execute("DELETE FROM beans WHERE bean_id = ? AND roaster_id = ?", (bean_id, roaster_id))
        if cursor.rowcount == 0:
            flash("Bean not found or you do not have permission to delete it.", "warning")
        else:
            flash("Bean deleted successfully.", "success")
        conn.commit()
    except sqlite3.Error as e:
        conn.rollback()
        app.logger.error(f"Database error deleting bean {bean_id} for roaster {roaster_id}: {e}")
        flash("An error occurred while deleting the bean.", "danger")
    finally:
        conn.close()
    return redirect(url_for('manage_roaster'))

@app.route('/delete_brew/<int:brewlog_id>', methods=['POST'])
@login_required
def delete_brew(brewlog_id):  # Renamed from delete_bean to delete_brew to avoid conflict
    if not hasattr(g, 'user') or not g.user or not hasattr(g.user, 'uid'):
        flash("User not properly authenticated.", "danger")
        return redirect(url_for('show_brew'))

    success, message = delete_brewlog_for_user(uid=g.user.uid, record_id=brewlog_id)

    if success:
        # The message from delete_brewlog_for_user might be too technical (e.g., "Deleted 1 record(s)")
        # We can customize it here.
        flash("Brew log entry deleted successfully.", "success")
    else:
        # If message indicates "Record not found" or similar, it's implicitly handled by rowcount in delete_brewlog_for_user
        # For other errors, the message from delete_brewlog_for_user is "Database operation failed" or "An error occurred..."
        flash(message, "danger") # Or a more user-friendly generic message like "Failed to delete brew log entry."

    return redirect(url_for('show_brew'))

@app.route('/save_brew', methods=['POST'])
@login_required
def save_brew():
    """
    Save a new brew record to the database.
    Requires user to be logged in and a valid bean_id to be provided.
    """
    from utils.error_handler import handle_validation_error, handle_database_error, handle_general_error

    conn = None
    try:
        data = request.get_json()
        uid = session.get('uid')

        # Validate required fields
        if not uid:
            return handle_validation_error(missing_fields=['User ID'])

        if not data or not isinstance(data, dict):
            return handle_validation_error(missing_fields=['Request data'])

        if 'bean_id' not in data:
            return handle_validation_error(missing_fields=['bean_id'])

        # Try to convert bean_id to integer to validate it
        try:
            bean_id = int(data['bean_id'])
        except (ValueError, TypeError):
            return handle_validation_error(invalid_fields=['bean_id'])

        # Build the SQL query dynamically based on provided fields
        fields = []
        values = []
        params = []

        # Always include uid and bean_id - ensure uid is stored as a string
        fields.extend(['uid', 'bean_id'])
        values.extend(['?', '?'])
        params.extend([str(uid), bean_id])  # Explicitly convert uid to string and use validated bean_id

        # Optional fields with validation
        optional_fields = [
            'bean_purchase_date', 'bean_roasting_date', 'brew_method',
            'grind_settings', 'brew_temp', 'PreInfusionTimeSec', 'brew_time',
            'coffee_dose', 'coffee_output', 'aroma', 'acidity', 'sweetness',
            'bitterness', 'body', 'aftertaste', 'overall_rating'
        ]

        # Numeric fields that should be validated
        numeric_fields = ['grind_settings', 'brew_temp', 'PreInfusionTimeSec', 'brew_time',
                         'coffee_dose', 'coffee_output', 'overall_rating']

        invalid_fields = []

        for field in optional_fields:
            if field in data and data[field] is not None:
                # Validate numeric fields
                if field in numeric_fields:
                    try:
                        # Convert to appropriate type
                        if field in ['PreInfusionTimeSec', 'brew_time', 'overall_rating']:
                            value = int(data[field])
                        else:
                            value = float(data[field])

                        fields.append(field)
                        values.append('?')
                        params.append(value)
                    except (ValueError, TypeError):
                        invalid_fields.append(field)
                else:
                    # For non-numeric fields, just add them
                    fields.append(field)
                    values.append('?')
                    params.append(data[field])

        # If we found invalid fields, return an error
        if invalid_fields:
            return handle_validation_error(invalid_fields=invalid_fields)

        # Add brew_date with today's date in d-m-yyyy format
        today = date.today().strftime('%d-%m-%Y')
        fields.append('brew_date')
        values.append('?')
        params.append(today)

        # Create a safe parameterized query
        # Create the SQL statement with placeholders
        placeholders = ', '.join(['?'] * len(fields))
        columns = ', '.join(fields)

        query = f"INSERT INTO brewlog ({columns}) VALUES ({placeholders})"

        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            cursor.execute(query, params)
            conn.commit()
            conn.close()
            return jsonify({'success': True})
        except sqlite3.Error as db_error:
            return handle_database_error(db_error, operation="save_brew",
                                        context={"fields": fields})
    except Exception as e:
        # Handle any other unexpected errors
        if conn:
            conn.close()
        return handle_general_error(e, operation="save_brew")

@app.route('/success')
@login_required
def success():
    return render_template('success.html',
                           default_arabica=session.get('default_arabica', '100'),
                           default_weight=session.get('default_weight', '1000'),
                           default_mix=session.get('default_mix', 'False'),
                           roaster_id=request.args.get('roaster_id'))

@app.route("/search_beans", methods=["GET"])
@login_required
def search_beans():
    # Check if user is logged in, redirect to home if not
    if not g.user:
        app.logger.warning("User not logged in, redirecting to home")
        return redirect(url_for('home'))

    app.logger.info("User logged in, proceeding with search_beans")

    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute("SELECT country FROM countries ORDER BY country")
    countries = cursor.fetchall()
    cursor.execute("SELECT c_flavor FROM coffee_flavors ORDER BY c_flavor")
    flavors = cursor.fetchall()
    # Add roasters query
    cursor.execute("SELECT roaster_id, name FROM roasters ORDER BY name")
    roasters = cursor.fetchall()
    # Add cities query
    cursor.execute("SELECT c_city FROM city_list ORDER BY c_city")
    cities = cursor.fetchall()
    conn.close()

    app.logger.info(f"Loaded data: {len(countries)} countries, {len(flavors)} flavors, {len(roasters)} roasters, {len(cities)} cities")

    show_login_modal = not g.user  # If user is not logged in, flag to open login modal
    return render_template("search_beans.html",
                           countries=countries, flavors=flavors, roasters=roasters, cities=cities,
                           show_login_modal=show_login_modal)



@app.route('/filter_beans', methods=['POST'])
@login_required
def filter_beans():
    data = request.get_json()

    # Normalize roasters field to always be a list
    roasters = data.get('roasters', [])
    app.logger.info(f"Roasters before normalization: {roasters}")
    if isinstance(roasters, str):
        roasters = [roasters]
    app.logger.info(f"Roasters after normalization: {roasters}")

    # Get cities and fetch roasters for those cities if needed
    cities = data.get('cities', [])
    if cities and not roasters:
        # If cities are selected but no roasters, fetch roasters for those cities
        conn = get_db_connection()
        cursor = conn.cursor()
        roaster_ids = []
        for city in cities:
            cursor.execute("SELECT roaster_id FROM roasters WHERE city = ?", (city,))
            city_roasters = cursor.fetchall()
            roaster_ids.extend([str(r['roaster_id']) for r in city_roasters])
        conn.close()

        # Add these roasters to the filter
        if roaster_ids:
            roasters = roaster_ids

    origin = data.get('origin', [])
    processing = data.get('processing', '')
    elevation = data.get('elevation', [])
    flavors = data.get('flavors', [])
    roast_level = data.get('roast_level', '')
    arabica_selected = int(data.get('arabica', 0))
    robusta_selected = int(data.get('robusta', 0))
    singleorigin = int(data.get('singleorigin', 0)) # Add single-origin parameter
    mix = int(data.get('mix', 0))
    speciality = int(data.get('speciality', 0))
    decaf = data.get('decaf', 0)
    selected_brew_methods = data.get('brew_methods', []) # Expect a list of strings
    # New parameter for flavor selection mode. Default is OR.
    flavor_mode = data.get('flavor_mode', 'OR').upper()

    query = """
        SELECT
            beans.bean_id,
            beans.roaster_id,
            beans.bean_name,
            beans.origin,
            beans.processing,
            beans.elevation,
            beans.body,
            beans.acidity,
            beans.flavors,
            beans.roast_level,
            beans.arabica,
            beans.robusta,
            beans.mix,
            beans.price,
            beans.weight,
            beans.turkish,
            beans.espresso,
            beans.french_press,
            beans.pour_over,
            beans.drip,
            beans.cold_brew,
            beans.Speciality AS speciality,
            beans.SCA_score,
            beans.decaf,
            roasters.name AS roaster_name,
            roasters.webpage AS roaster_webpage
        FROM beans
        JOIN roasters ON beans.roaster_id = roasters.roaster_id
        WHERE 1=1
    """
    params = []

    if origin:
        stripped_origins = [o.strip() for o in origin]
        query += " AND (" + " OR ".join(["beans.origin LIKE ?"] * len(stripped_origins)) + ")"
        params.extend([f"%{o}%" for o in stripped_origins])
    if processing:
        query += " AND beans.processing = ?"
        params.append(processing)
    if flavors:
        joiner = " OR " if flavor_mode != "AND" else " AND "
        query += " AND (" + joiner.join(["beans.flavors LIKE ?"] * len(flavors)) + ")"
        params.extend([f"%{f}%" for f in flavors])
    if roast_level:
        query += " AND beans.roast_level = ?"
        params.append(roast_level)

    if roasters:
        roaster_ids = [int(r) for r in roasters]
        query += f" AND beans.roaster_id IN ({','.join(['?'] * len(roaster_ids))})"
        params.extend(roaster_ids)

    # Enhanced arabica/robusta filtering logic
    if arabica_selected and robusta_selected:
        # Both selected: return only beans that contain both arabica AND robusta
        query += " AND beans.arabica > 0 AND beans.robusta > 0"
    elif arabica_selected:
        # Only arabica selected: return pure arabica beans (no robusta)
        query += " AND beans.arabica > 0 AND beans.robusta = 0"
    elif robusta_selected:
        # Only robusta selected: return pure robusta beans (no arabica)
        query += " AND beans.robusta > 0 AND beans.arabica = 0"

    if singleorigin == 1:
        query += " AND beans.mix = 0"  # Single-origin means mix is 0
    if mix == 1:
        query += " AND beans.mix = 1"
    if speciality:
        query += " AND beans.speciality = 1"
    if decaf:
        query += " AND beans.decaf = 1"

    # selected_brew_methods and its log are assumed to be immediately before this block
    valid_brew_methods = ['turkish', 'espresso', 'french_press', 'pour_over', 'drip', 'cold_brew']
    active_brew_methods = [m for m in selected_brew_methods if m in valid_brew_methods]
    # Ensure the log for active_brew_methods is present if it was added in a previous step, or add it:

    if not active_brew_methods:
        # If no valid brew methods are selected (e.g., selected_brew_methods was empty or contained only invalid values)
        query += " AND 1=0" # Ensures no beans are returned
    else:
        # If there are active brew methods, filter by all of them (AND logic)
        for method in active_brew_methods:
            query += f" AND beans.{method} = 1"

    if elevation:
        if elevation == 'altd_1':
            query += " AND CAST(beans.elevation AS INTEGER) > ? AND CAST(beans.elevation AS INTEGER) < ?"
            params.append(0)
            params.append(1000)
        elif elevation == 'altd_2':
            query += " AND CAST(beans.elevation AS INTEGER) > ? AND CAST(beans.elevation AS INTEGER) < ?"
            params.append(1001)
            params.append(1500)
        elif elevation == 'altd_3':
            query += " AND CAST(beans.elevation AS INTEGER) > ? AND CAST(beans.elevation AS INTEGER) < ?"
            params.append(1501)
            params.append(2000)
        elif elevation == 'altd_4':
            query += " AND CAST(beans.elevation AS INTEGER) > ?"
            params.append(2001)

    sort_by = data.get('sort_by')
    # Validate sort_dir against an allowlist
    sort_dir_input = data.get('sort_dir', 'asc').upper()
    sort_dir = 'ASC' if sort_dir_input not in ('ASC', 'DESC') else sort_dir_input

    if sort_by in ('arabica', 'robusta'):
        # Use the validated sort_dir
        query += f" ORDER BY beans.{sort_by} {sort_dir}"

    conn = get_db_connection()
    cursor = conn.cursor()
    try:
        cursor.execute(query, params)
        rows = cursor.fetchall()
        beans = []
        for row in rows:
            beans.append({
                "bean_id": row["bean_id"],
                "roaster_id": row["roaster_id"],
                "bean_name": row["bean_name"],
                "origin": row["origin"],
                "processing": row["processing"],
                "elevation": row["elevation"],
                "body": row["body"],  # Include body field
                "acidity": row["acidity"],  # Include acidity field
                "flavors": row["flavors"],
                "roast_level": row["roast_level"],
                "arabica": row["arabica"],
                "robusta": row["robusta"],
                "mix": row["mix"],
                "price": row["price"],
                "weight": row["weight"],
                "turkish": row["turkish"],
                "espresso": row["espresso"],
                "french_press": row["french_press"],
                "pour_over": row["pour_over"],
                "drip": row["drip"],
                "cold_brew": row["cold_brew"],
                "speciality": row["speciality"],
                "SCA_score": row["SCA_score"],  # Ensure SCA_score is included
                "roaster_name": row["roaster_name"],
                "roaster_webpage": row["roaster_webpage"]  # Include roaster_webpage
            })

    ##    print(f"Found {len(beans)} matching beans")  # Debug: Log found results count

        conn.close()
        return jsonify(beans)
    except Exception as e:
        app.logger.error(f"Error filtering beans: {str(e)}", exc_info=True)
        if 'conn' in locals() and conn:
            try:
                conn.close()
            except Exception as db_close_e:
                app.logger.error(f"Error closing DB connection during exception handling: {db_close_e}")
        return jsonify({"error": "An internal error occurred while filtering beans."}), 500

@app.route("/test_static")
def test_static():
    """Simple test endpoint to verify static files are being served correctly"""
    try:
        app.logger.info("test_static endpoint called")

        # Get the absolute path to the static folder
        static_folder = app.static_folder
        app.logger.info(f"Static folder: {static_folder}")

        # Create a simple HTML response
        html = """
        <html>
        <head>
            <title>Static File Test</title>
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
        </head>
        <body>
            <h1>Static File Test</h1>
            <p>Testing if static files are being served correctly.</p>

            <h2>Testing direct URL access:</h2>
            <p>Click to test: <a href="/static/js/update_results.js" target="_blank">/static/js/update_results.js</a></p>
            <p>Click to test: <a href="/static/test.txt" target="_blank">/static/test.txt</a></p>

            <h2>Testing with fetch API:</h2>
            <div id="fetch-result">Testing...</div>

            <script>
                // Test with absolute path
                fetch('/static/js/update_results.js')
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`Failed to load update_results.js: ${response.status} ${response.statusText}`);
                        }
                        document.getElementById('fetch-result').innerHTML += '<p style="color:green">Successfully loaded update_results.js</p>';
                    })
                    .catch(error => {
                        document.getElementById('fetch-result').innerHTML += `<p style="color:red">${error.message}</p>`;
                    });

                // Test with test.txt
                fetch('/static/test.txt')
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`Failed to load test.txt: ${response.status} ${response.statusText}`);
                        }
                        document.getElementById('fetch-result').innerHTML += '<p style="color:green">Successfully loaded test.txt</p>';
                    })
                    .catch(error => {
                        document.getElementById('fetch-result').innerHTML += `<p style="color:red">${error.message}</p>`;
                    });
            </script>
        </body>
        </html>
        """
        return html
    except Exception as e:
        app.logger.error(f"Error in test_static: {str(e)}")
        return f"Error: {str(e)}", 500

@app.route('/test_simple')
def test_simple():
    """A very simple test endpoint that doesn't rely on any static files"""
    try:
        app.logger.info("test_simple endpoint called")
        return """
        <html>
        <head>
            <title>Simple Test</title>
        </head>
        <body>
            <h1>Simple Test</h1>
            <p>This is a simple test page that doesn't rely on any static files.</p>
            <p>If you can see this, the Flask application is working correctly.</p>
        </body>
        </html>
        """
    except Exception as e:
        app.logger.error(f"Error in test_simple: {str(e)}")
        return f"Error: {str(e)}", 500

@app.route('/fallback/update_results.js')
def serve_update_results_js():
    """Fallback route to serve the update_results.js file directly"""
    try:
        app.logger.info("serve_update_results_js endpoint called")
        js_path = os.path.join(app.static_folder, 'js', 'update_results.js')

        if os.path.isfile(js_path):
            with open(js_path, 'r') as f:
                js_content = f.read()

            app.logger.info(f"Successfully read update_results.js, size: {len(js_content)} bytes")
            return js_content, 200, {'Content-Type': 'application/javascript'}
        else:
            app.logger.error(f"update_results.js not found at {js_path}")
            return "// File not found", 404, {'Content-Type': 'application/javascript'}
    except Exception as e:
        app.logger.error(f"Error serving update_results.js: {str(e)}")
        return f"// Error: {str(e)}", 500, {'Content-Type': 'application/javascript'}

@app.route('/get_roaster_details/<int:roaster_id>')
@login_required
def get_roaster_details(roaster_id):
    try:
        conn = sqlite3.connect(DATABASE)
        cursor = conn.cursor()
        cursor.execute("""
            SELECT
                name,
                COALESCE(address, '') as address,
                COALESCE(city, '') as city,
                COALESCE(zip, '') as zip,
                COALESCE(email, '') as email,
                COALESCE(webpage, '') as webpage,
                COALESCE(minimun_shipping, '') as minimun_shipping,
                COALESCE(shipping_cost, '') as shipping_cost
            FROM roasters
            WHERE roaster_id = ?
        """, (roaster_id,))
        row = cursor.fetchone()
        cursor.close()
        conn.close()
        if row:
            data = {
                'name': row[0],
                'address': row[1] or 'לא זמין',
                'city': row[2] or 'לא זמין',
                'zip': row[3] or 'לא זמין',
                'email': row[4] or 'לא זמין',
                'website': row[5] or 'לא זמין',
                'minimum_shipping': row[6] or 'לא זמין',
                'shipping_cost': row[7] or 'לא זמין'
            }
            return jsonify(data)
        return jsonify({'error': 'Roaster not found'}), 404
    except Exception as e:
        app.logger.error(f"Database error in get_roaster_details: {str(e)}", exc_info=True)
        if 'conn' in locals():
            conn.close()
        return jsonify({'error': 'Database error', 'message': str(e)}), 500

# Fix the show_brew route and other errors

@app.route('/show_brew', methods=['GET'])
@login_required
def show_brew():
    """
    Display the user's brew logs.
    Requires user to be logged in.
    """
    from utils.error_handler import handle_database_error, handle_general_error

    uid = session.get('uid')
    conn = None

    try:
        if not uid:
            # If no UID in session, just show an empty page
            return render_template('showBrew.html', brewlogs=[], error="No user ID found in session")

        try:
            conn = get_db_connection()
            cursor = conn.cursor()

            # Use a parameterized query with no string formatting
            query = '''
                SELECT
                    brewlog.*,
                    beans.bean_name,
                    beans.origin,
                    beans.processing,
                    beans.roast_level,
                    roasters.name as roaster_name
                FROM
                    brewlog
                JOIN
                    beans ON brewlog.bean_id = beans.bean_id
                JOIN
                    roasters ON beans.roaster_id = roasters.roaster_id
                WHERE
                    brewlog.uid = ?
                ORDER BY
                    substr(brewlog.brew_date, 7, 4) || '-' || substr(brewlog.brew_date, 4, 2) || '-' || substr(brewlog.brew_date, 1, 2) DESC
            '''

            # Always convert uid to string for consistency
            str_uid = str(uid)
            cursor.execute(query, (str_uid,))
            brew_records = cursor.fetchall()

            brewlogs = [dict(row) for row in brew_records]

            # Reformat date fields to dd-mm-YY (last digit of year)
            for brewlog in brewlogs:
                # Process brew_date
                if 'brew_date' in brewlog and brewlog['brew_date']:
                    try:
                        # Assuming date format is dd-mm-yyyy
                        parts = brewlog['brew_date'].split('-')
                        if len(parts) == 3 and len(parts[2]) == 4:  # Make sure it's a valid date with 4-digit year
                            # Keep only the last digit of the year
                            brewlog['brew_date'] = f"{parts[0]}-{parts[1]}-{parts[2][-2:]}"
                    except Exception:
                        # Just keep the original format if there's an error
                        pass

                # Process bean_roasting_date
                if 'bean_roasting_date' in brewlog and brewlog['bean_roasting_date']:
                    try:
                        # Assuming date format is dd-mm-yyyy
                        parts = brewlog['bean_roasting_date'].split('/')
                        if len(parts) == 3 and len(parts[2]) == 4:  # Make sure it's a valid date with 4-digit year
                            # Keep only the last digit of the year
                            brewlog['bean_roasting_date'] = f"{parts[0]}-{parts[1]}-{parts[2][-2:]}"
                    except Exception:
                        # Just keep the original format if there's an error
                        pass

                # Process bean_purchase_date
                if 'bean_purchase_date' in brewlog and brewlog['bean_purchase_date']:
                    try:
                        # Assuming date format is dd-mm-yyyy
                        parts = brewlog['bean_purchase_date'].split('/')
                        if len(parts) == 3 and len(parts[2]) == 4:  # Make sure it's a valid date with 4-digit year
                            # Keep only the last digit of the year
                            brewlog['bean_purchase_date'] = f"{parts[0]}-{parts[1]}-{parts[2][-2:]}"
                    except Exception:
                        # Just keep the original format if there's an error
                        pass

            conn.close()
            return render_template('showBrew.html', brewlogs=brewlogs)

        except sqlite3.Error as db_error:
            # Handle database errors
            if conn:
                conn.close()
            # For template rendering, we'll handle the error differently than API endpoints
            error_message = "Database error occurred while fetching your brew logs."
            return render_template('showBrew.html', brewlogs=[], error=error_message)

    except Exception as e:
        # Handle any other unexpected errors
        if conn:
            conn.close()
        # For template rendering, we'll handle the error differently than API endpoints
        error_message = "An unexpected error occurred while fetching your brew logs."
        return render_template('showBrew.html', brewlogs=[], error=error_message)

# Fix the check_session endpoint
@app.route('/check_session', methods=['GET'])
def check_session():
    """Check if user is authenticated in the current session"""
    try:
        if g.user:
            return jsonify({'authenticated': True, 'user': g.user.display_name})
        else:
            return jsonify({'authenticated': False})
    except Exception as e:
        app.logger.error(f"Error in check_session: {str(e)}", exc_info=True)
        return jsonify({'authenticated': False, 'error': "An internal error occurred."})

@app.route('/get_firebase_config', methods=['GET'])
def get_firebase_config():
    """Provide Firebase configuration to the client, ensuring environment variables are set."""
    api_key = os.environ.get('FIREBASE_API_KEY')
    auth_domain = os.environ.get('FIREBASE_AUTH_DOMAIN')
    project_id = os.environ.get('FIREBASE_PROJECT_ID')

    if not all([api_key, auth_domain, project_id]):
        app.logger.error("Error: Missing one or more Firebase environment variables (FIREBASE_API_KEY, FIREBASE_AUTH_DOMAIN, FIREBASE_PROJECT_ID)") # Changed to app.logger.error
        return jsonify({"error": "Server configuration error: Missing Firebase credentials."}), 500

    config = {
        'apiKey': api_key,
        'authDomain': auth_domain,
        'projectId': project_id
    }
    # Add other necessary config values if needed, loaded from env vars
    return jsonify(config)

@app.route('/get_csrf_token', methods=['GET'])
def get_csrf_token():
    """Provide CSRF token for frontend requests"""
    from flask_wtf.csrf import generate_csrf
    return jsonify({'csrf_token': generate_csrf()})

@app.route('/test-smtp', methods=['POST'])
def test_smtp_production():
    """Test SMTP configuration in production environment - ADMIN ONLY"""
    try:
        # Basic security check - only allow if user is admin
        if not getattr(g, 'is_admin', False):
            return jsonify({'success': False, 'message': 'Admin access required'}), 403

        data = request.get_json()
        test_email = data.get('email', '<EMAIL>')

        # Check environment variables
        required_vars = ['ZOHO_SMTP_PASSWORD', 'SECRET_KEY']
        env_status = {}

        for var in required_vars:
            value = os.environ.get(var)
            env_status[var] = {
                'exists': bool(value),
                'length': len(value) if value else 0
            }

        # Test email content
        test_subject = "Israeli Coffee - Production SMTP Test"
        test_message = f"""
        <html>
          <head>
            <style>
              body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
              .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
              .header {{ background-color: #8B4513; color: white; padding: 20px; text-align: center; }}
              .content {{ padding: 20px; background-color: #f9f9f9; }}
              .success {{ color: #28a745; font-weight: bold; }}
              .info {{ background-color: #e9ecef; padding: 10px; border-radius: 5px; margin: 10px 0; }}
            </style>
          </head>
          <body>
            <div class="container">
              <div class="header">
                <h1>🚀 Production SMTP Test</h1>
              </div>
              <div class="content">
                <div class="success">
                  <h2>✅ Production Email System Working!</h2>
                </div>

                <p>This email confirms that your production SMTP configuration is working correctly.</p>

                <div class="info">
                  <h3>Environment Status:</h3>
                  <ul>
                    <li>ZOHO_SMTP_PASSWORD: {'✅ Found' if env_status['ZOHO_SMTP_PASSWORD']['exists'] else '❌ Missing'} ({env_status['ZOHO_SMTP_PASSWORD']['length']} chars)</li>
                    <li>SECRET_KEY: {'✅ Found' if env_status['SECRET_KEY']['exists'] else '❌ Missing'} ({env_status['SECRET_KEY']['length']} chars)</li>
                  </ul>
                </div>

                <p><strong>✅ Production Ready:</strong> Your email verification and password reset emails will now be sent through your production SMTP server.</p>

                <p>Test completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')}</p>
              </div>
            </div>
          </body>
        </html>
        """

        app.logger.info(f"🧪 Testing SMTP in production environment for admin user")
        app.logger.info(f"🧪 Environment variables status: {env_status}")

        # Attempt to send test email with debug enabled
        email_sent = send_email(
            recipient=test_email,
            subject=test_subject,
            message=test_message,
            debug=True  # Enable debug output for production testing
        )

        if email_sent:
            app.logger.info(f"✅ Production SMTP test successful - email sent to {test_email}")
            return jsonify({
                'success': True,
                'message': f'Test email sent successfully to {test_email}',
                'environment_status': env_status
            })
        else:
            app.logger.error(f"❌ Production SMTP test failed - could not send email to {test_email}")
            return jsonify({
                'success': False,
                'message': 'Failed to send test email - check server logs for details',
                'environment_status': env_status
            }), 500

    except Exception as e:
        app.logger.error(f"❌ Production SMTP test error: {e}", exc_info=True)
        return jsonify({
            'success': False,
            'message': f'SMTP test error: {str(e)}'
        }), 500

@app.route('/admin/test-smtp')
def smtp_test_page():
    """Display SMTP test page for admins"""
    return render_template('test_smtp.html')

@app.route('/set_roaster_id', methods=['POST'])
@login_required
def set_roaster_id():
    roaster_id = request.form.get('roaster_id')
    if not roaster_id:
        return "Missing roaster_id", 400
    session['current_roaster_id'] = roaster_id
    return redirect(url_for('manage_roaster'))

@app.route('/manage_roaster')
@login_required
def manage_roaster():
    # Get roaster_id from session
    roaster_id = session.get('roaster_id') # Use the key set by before_request
    if not roaster_id:
        return redirect(url_for('home'))
    # Set current_roaster_id in session for edit_roaster
    session['current_roaster_id'] = roaster_id
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Get roaster details
        cursor.execute("""
            SELECT
                name, address, city, zip, email, webpage,
                minimun_shipping, shipping_cost, uid
            FROM roasters
            WHERE roaster_id = ?
        """, (roaster_id,))
        roaster = cursor.fetchone()

        if not roaster:
            conn.close()
            return "Roaster not found", 404

        # Get this roaster's beans
        cursor.execute("""
            SELECT
                bean_id, bean_name, origin, processing,
                flavors, acidity, after_taste, roast_level,
                body, arabica, robusta, mix, price, weight,
                Speciality, SCA_score, decaf, image_file
            FROM beans
            WHERE roaster_id = ?
            ORDER BY bean_name
        """, (roaster_id,))
        beans = cursor.fetchall()

        conn.close()

        return render_template(
            'manage_roaster.html',
            roaster=roaster,
            beans=beans,
            roaster_id=roaster_id
        )

    except Exception as e:
        app.logger.error(f"Error in manage_roaster: {e}", exc_info=True)
        if 'conn' in locals():
            conn.close()
        return f"Error loading roaster data: {str(e)}", 500

@app.route('/link_roaster', methods=['GET', 'POST'])
@admin_required # Changed from @login_required; admin check is now handled by the decorator
def link_roaster():
    # The @admin_required decorator handles the permission check.
    # Old placeholder comments for admin check can be removed or kept as historical.

    conn = None  # Initialize connection to None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        if request.method == 'POST':
            data = request.get_json() # Get data from JSON body
            email = data.get('email')
            roaster_id = data.get('roaster_id')

            if not email or not roaster_id:
                # Return JSON for client-side handling
                return jsonify({'success': False, 'message': "Email and Roaster selection are required."})

            try:
                # Find the user by email in Firebase
                user = auth.get_user_by_email(email)
                user_uid = user.uid

                # Update the roaster record with the UID
                cursor.execute("UPDATE roasters SET uid = ? WHERE roaster_id = ?", (user_uid, roaster_id))
                # Linking successful, return JSON response
                conn.commit()
                return jsonify({'success': True, 'message': f"Successfully linked roaster ID {roaster_id} to user {email} (UID: {user_uid})."})

            except auth.UserNotFoundError:
                return jsonify({'success': False, 'message': f"User with email {email} not found in Firebase."})
            except sqlite3.Error as db_err:
                conn.rollback() # Rollback changes on DB error
                app.logger.error(f"Database error linking roaster {roaster_id} to email {email}: {db_err}", exc_info=True) # Changed to app.logger.error
                return jsonify({'success': False, 'message': "A database error occurred."}) # Generic error
            except Exception as e:
                app.logger.error(f"Error linking roaster {roaster_id} to email {email}: {e}", exc_info=True) # Changed to app.logger.error
                return jsonify({'success': False, 'message': "An unexpected error occurred."}) # Generic error

            # This part should ideally not be reached if linking was attempted
            return jsonify({'success': False, 'message': 'Linking process did not complete.'})

        # GET request: Fetch roasters to display in the dropdown
        form = LinkRoasterForm() # Instantiate the form
        cursor.execute("SELECT roaster_id, name FROM roasters ORDER BY name")
        roasters = cursor.fetchall()
        # Pass the form object to the template
        return render_template('link_roaster.html', roasters=roasters, form=form)

    finally:
        if conn:
            conn.close()

# Define a simple form for CSRF protection in the link_roaster route
class LinkRoasterForm(FlaskForm):
    pass

# Replace the display_roaster route to use token instead of integer
@app.route('/display_roaster/<token>')
def display_roaster(token):
    secret_key = os.environ.get("FLASK_SECRET_KEY", "a_default_secret_key_for_development")
    try:
        roaster_id = (decrypt_id(secret_key, token)/8457)
    except Exception:
        return "Invalid roaster token", 400

    # Use the DATABASE variable for the path
    conn = sqlite3.connect(DATABASE)
    cursor = conn.cursor()
    cursor.execute("SELECT roaster_id, name, email FROM roasters WHERE roaster_id = ?", (roaster_id,))
    row = cursor.fetchone()
    if not row:
        conn.close()
        return "Roaster not found", 404
    roaster = {'roaster_id': row[0], 'name': row[1], 'email': row[2]}

    cursor.execute("SELECT * FROM beans WHERE roaster_id = ?", (roaster_id,))
    beans_rows = cursor.fetchall()
    # Map beans_rows to dicts as needed for your template
    beans = [
        {
            'bean_name': r[2],
            'origin': r[3],
            'flavors': r[6],
            'roast_level': r[9],
            'arabica': r[11],
            'robusta': r[12],
            'mix': r[19],
            'speciality': r[24],
            'decaf': r[25],
            'price': r[20],
            'weight': r[21]
        }
        for r in beans_rows
    ]
    conn.close()
    return render_template('display_roaster.html', roaster=roaster, beans=beans)

@app.route('/edit_roaster', methods=['GET', 'POST'])
@login_required
def edit_roaster():
    # Get roaster_id from session only (do not use request.args)
    roaster_id = session.get('current_roaster_id')
    if not roaster_id:
        return redirect(url_for('home'))

    # Verify user is authorized for this roaster
    if not session.get('roaster_user') or session.get('roaster_id') != roaster_id:
        return redirect(url_for('home'))

    conn = get_db_connection()
    cursor = conn.cursor()

    # Get roaster details for editing
    cursor.execute("""
        SELECT
            name, address, city, zip, email, webpage,
            minimun_shipping, shipping_cost, uid
        FROM roasters
        WHERE roaster_id = ?
    """, (roaster_id,))
    roaster = cursor.fetchone()
    conn.close()

    if not roaster:
        return "Roaster not found", 404

    if request.method == 'POST':
        # Update roaster details
        name = request.form['name']
        address = request.form['address']
        city = request.form['city']
        zip = request.form['zip']
        email = request.form['email']
        webpage = request.form['webpage']
        minimun_shipping = request.form['minimun_shipping']
        shipping_cost = request.form['shipping_cost']

        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute('''
            UPDATE roasters SET
                name = ?, address = ?, city = ?, zip = ?, email = ?,
                webpage = ?, minimun_shipping = ?, shipping_cost = ?
            WHERE roaster_id = ?
        ''', (name, address, city, zip, email, webpage, minimun_shipping, shipping_cost, roaster_id))
        conn.commit()
        conn.close()

        session['current_roaster_id'] = roaster_id
        return redirect(url_for('manage_roaster'))

    return render_template('edit_roaster.html', roaster=roaster, roaster_id=roaster_id)

@app.route('/send_contact_email', methods=['POST'])
def send_contact_email():
    if request.method == 'POST':
        # Retrieve form data
        name = request.form.get('name')
        email_from_form = request.form.get('email') # Renamed to avoid conflict
        subject_from_form = request.form.get('subject') # Renamed
        message_content = request.form.get('message') # Renamed

        # Validate form data
        if not all([name, email_from_form, subject_from_form, message_content]):
            return jsonify({
                'success': False,
                'message': "כל השדות הם חובה. אנא מלא את כל הפרטים."
            })

        # Pre-process message content for HTML (replace newlines with <br>)
        message_content_html = message_content.replace('\n', '<br>')

        # Construct HTML message with RTL support
        html_message = f"""
        <html>
          <head>
            <style>
              body {{ direction: rtl; text-align: right; }}
              p, h3 {{ text-align: right; }}
            </style>
          </head>
          <body>
            <h3>פנייה חדשה מטופס יצירת קשר</h3>
            <p><strong>שם משתמש</strong> {name}</p>
            <p><a href="mailto:{email_from_form}">{email_from_form}</a> <strong>:דוא"ל</strong></p>
            <hr>
            <p><strong>נושא</strong> {subject_from_form}</p>
            <p><strong>הודעה</strong></p>
            <p>{message_content_html}</p>
          </body>
        </html>
        """

        recipient_email = "<EMAIL>"

        try:
            email_sent = send_email(
                recipient=recipient_email,
                subject=subject_from_form,
                message=html_message
            )

            if email_sent:
                response = jsonify({
                    'success': True,
                    'message': 'ההודעה נשלחה בהצלחה! נחזור אליך בהקדם.',
                    'redirect_url': url_for('home')
                })
                response.headers['Content-Type'] = 'application/json'
                return response, 200
            else:
                return jsonify({
                    'success': False,
                    'message': "אירעה שגיאה בשליחת ההודעה. אנא נסה שוב מאוחר יותר."
                })

        except Exception as e:
            app.logger.error(f"Error sending contact form email: {str(e)}", exc_info=True)
            return jsonify({
                'success': False,
                'message': "אירעה שגיאה בשליחת ההודעה. אנא נסה שוב מאוחר יותר."
            })

    return jsonify({
        'success': False,
        'message': "Invalid request method."
    })

@app.route('/roaster_letter')
def roaster_letter():
    return render_template('roaster_letter.html')

@app.route('/api/roasters_csv')
def api_roasters_csv():
    csv_path = os.path.join(os.path.dirname(__file__), 'utils', 'roasters links.csv')
    roasters = []
    with open(csv_path, encoding='utf-8-sig') as f:
        reader = csv.DictReader(f)
        for row in reader:
            roasters.append({
                'roaster_id': row['roaster_id'],
                'roaster_name': row['roaster name'],
                'roaster_email': row['roaster email'],
                'roaster_link': row['roaster link']
            })
    return jsonify(roasters)

@app.route('/api/israeli_coffee_letter')
def api_israeli_coffee_letter():
    letter_path = os.path.join(os.path.dirname(__file__), 'israeli_coffee_letter.txt')
    with open(letter_path, encoding='utf-8') as f:
        paragraphs = [p.strip() for p in f.read().split('\n\n') if p.strip()]
    return jsonify(paragraphs)

# Add a new route to show the email verification notice
@app.route('/verify-email')
def verify_email_notice():
    # Check if user is logged in but not verified
    user = getattr(g, 'user', None)
    if not user:
        # Not logged in, redirect home
        return redirect(url_for('home'))
    if user.email_verified:
        # Already verified, redirect home
        flash("Your email is already verified.", "success")
        return redirect(url_for('home'))

    return render_template('verify_email.html')

# New backend route for sending email verification
@app.route('/send-verification-email', methods=['POST'])
@csrf.exempt  # Exempt from CSRF protection for registration flow
def send_verification_email():
    """Send email verification link via backend instead of Firebase"""
    try:
        # Manual authentication check instead of @login_required decorator
        # This allows unverified users to request verification emails
        if 'id_token' not in session:
            return jsonify({'success': False, 'message': 'User not authenticated'}), 401

        try:
            # Verify the token and get user info
            token_details = auth.verify_id_token(session['id_token'], clock_skew_seconds=60)
            user = auth.get_user(token_details['uid'])
        except Exception as e:
            app.logger.error(f"Error verifying user token in send_verification_email: {e}")
            return jsonify({'success': False, 'message': 'Invalid authentication'}), 401

        if not user:
            return jsonify({'success': False, 'message': 'User not found'}), 401

        if user.email_verified:
            return jsonify({'success': False, 'message': 'Email already verified'}), 400

        # Generate verification token
        app.logger.info(f"Generating verification token for user: {user.email}")
        token = generate_email_verification_token(user.email, user.uid)

        # Create verification URL
        verification_url = url_for('verify_email_backend', token=token, _external=True)
        app.logger.info(f"Generated verification URL: {verification_url}")

        # Create email content
        from utils.email_actions import create_verification_email_html
        email_html = create_verification_email_html(verification_url, user.email)
        app.logger.info(f"Generated email HTML content (length: {len(email_html)} chars)")

        # Send email
        app.logger.info(f"Attempting to send verification email to: {user.email}")
        email_sent = send_email(
            recipient=user.email,
            subject="Israeli Coffee - Verify Your Email Address",
            message=email_html
        )

        if email_sent:
            app.logger.info(f"✅ Verification email sent successfully to {user.email}")
            return jsonify({'success': True, 'message': 'Verification email sent successfully'})
        else:
            app.logger.error(f"❌ Failed to send verification email to {user.email}")
            return jsonify({'success': False, 'message': 'Failed to send verification email. Please check server logs.'}), 500

    except Exception as e:
        app.logger.error(f"Error sending verification email: {e}", exc_info=True)
        return jsonify({'success': False, 'message': 'An error occurred while sending verification email'}), 500

# New backend route for handling email verification
@app.route('/verify-email/<token>')
def verify_email_backend(token):
    """Handle email verification when user clicks the link"""
    try:
        from utils.email_actions import verify_email_token, EmailActionError

        # Verify the token
        payload = verify_email_token(token)
        email = payload['email']
        uid = payload['uid']

        # Get the user from Firebase
        try:
            user = auth.get_user(uid)
            if user.email != email:
                flash("Invalid verification link - email mismatch", "danger")
                return redirect(url_for('home'))

            # Mark email as verified in Firebase
            auth.update_user(uid, email_verified=True)

            flash("Email verified successfully! You can now access all features.", "success")
            app.logger.info(f"Email verified for user {email}")

            return redirect(url_for('home'))

        except auth.UserNotFoundError:
            flash("User not found", "danger")
            return redirect(url_for('home'))

    except EmailActionError as e:
        flash(str(e), "danger")
        return redirect(url_for('home'))
    except Exception as e:
        app.logger.error(f"Error verifying email: {e}", exc_info=True)
        flash("An error occurred during email verification", "danger")
        return redirect(url_for('home'))

# New backend route for password reset form
@app.route('/reset-password/<token>')
def reset_password_form(token):
    """Display password reset form when user clicks the reset link"""
    try:
        from utils.email_actions import verify_password_reset_token, EmailActionError

        # Verify the token
        payload = verify_password_reset_token(token)
        email = payload['email']
        uid = payload['uid']

        # Verify user still exists
        try:
            user = auth.get_user(uid)
            if user.email != email:
                flash("Invalid password reset link - email mismatch", "danger")
                return redirect(url_for('home'))

            # Token is valid, show the password reset form
            return render_template('reset_password_form.html', token=token, email=email)

        except auth.UserNotFoundError:
            flash("User not found", "danger")
            return redirect(url_for('home'))

    except EmailActionError as e:
        flash(str(e), "danger")
        return redirect(url_for('home'))
    except Exception as e:
        app.logger.error(f"Error displaying password reset form: {e}", exc_info=True)
        flash("An error occurred while accessing the password reset form", "danger")
        return redirect(url_for('home'))

# New backend route for processing password reset
@app.route('/update-password', methods=['POST'])
def update_password():
    """Process the new password from the reset form"""
    try:
        from utils.email_actions import verify_password_reset_token, EmailActionError

        token = request.form.get('token')
        new_password = request.form.get('new_password')
        confirm_password = request.form.get('confirm_password')

        if not all([token, new_password, confirm_password]):
            flash("All fields are required", "danger")
            return redirect(url_for('reset_password_form', token=token))

        if new_password != confirm_password:
            flash("Passwords do not match", "danger")
            return redirect(url_for('reset_password_form', token=token))

        if len(new_password) < 6:
            flash("Password must be at least 6 characters long", "danger")
            return redirect(url_for('reset_password_form', token=token))

        # Verify the token
        payload = verify_password_reset_token(token)
        email = payload['email']
        uid = payload['uid']

        # Update the password in Firebase
        try:
            auth.update_user(uid, password=new_password)
            flash("Password updated successfully! You can now log in with your new password.", "success")
            app.logger.info(f"Password reset completed for user {email}")
            return redirect(url_for('home'))

        except auth.UserNotFoundError:
            flash("User not found", "danger")
            return redirect(url_for('home'))
        except Exception as firebase_error:
            app.logger.error(f"Firebase error updating password for {email}: {firebase_error}")
            flash("Error updating password. Please try again.", "danger")
            return redirect(url_for('reset_password_form', token=token))

    except EmailActionError as e:
        flash(str(e), "danger")
        return redirect(url_for('home'))
    except Exception as e:
        app.logger.error(f"Error updating password: {e}", exc_info=True)
        flash("An error occurred while updating your password", "danger")
        return redirect(url_for('home'))

@app.route('/edit_price', methods=['GET', 'POST'])
@login_required
def edit_price():
    # Get all roasters for the dropdown
    conn = get_db_connection()
    cursor = conn.cursor()

    # Always show all roasters
    cursor.execute("SELECT roaster_id, name FROM roasters ORDER BY name")
    roasters = cursor.fetchall()

    # Get the selected roaster_id from the request
    selected_roaster_id = request.args.get('roaster_id')
    if request.method == 'POST':
        selected_roaster_id = request.form.get('roaster_id')

    beans = []
    selected_roaster_name = ""

    # If a roaster is selected, get its beans
    if selected_roaster_id:
        # Get roaster name
        cursor.execute("SELECT name FROM roasters WHERE roaster_id = ?", (selected_roaster_id,))
        roaster_row = cursor.fetchone()
        if roaster_row:
            selected_roaster_name = roaster_row['name']

        # Get beans for this roaster
        cursor.execute("""
            SELECT bean_id, bean_name, weight, price
            FROM beans
            WHERE roaster_id = ?
            ORDER BY bean_name
        """, (selected_roaster_id,))
        beans = cursor.fetchall()

    # Handle form submission to update prices
    if request.method == 'POST' and 'bean_ids[]' in request.form:
        # Authorization check: Only roaster users can update prices
        if not session.get('roaster_user'):
            flash("You must be a roaster to edit prices.", "danger")
            conn.close()
            return redirect(url_for('home')) # Or appropriate redirect

        auth_roaster_id = session.get('roaster_id')
        if not auth_roaster_id:
            flash("Roaster ID not found in your session. Cannot edit prices.", "danger")
            conn.close()
            return redirect(url_for('home')) # Or appropriate redirect

        bean_ids = request.form.getlist('bean_ids[]')
        prices = request.form.getlist('prices[]')

        updated_count = 0
        error_occurred = False
        try:
            # Update each bean's price, ensuring it belongs to the authenticated roaster
            for i in range(len(bean_ids)):
                bean_id = bean_ids[i]
                price = prices[i] # Consider validating price (e.g., is numeric, non-negative)

                # It's safer to also ensure selected_roaster_id (if used for display) matches auth_roaster_id
                # or that the beans being edited indeed belong to auth_roaster_id
                if selected_roaster_id and str(selected_roaster_id) != str(auth_roaster_id):
                    flash("Mismatch in roaster context. Operation aborted for safety.", "danger")
                    error_occurred = True
                    break # Stop processing further beans

                cursor.execute("""
                    UPDATE beans
                    SET price = ?
                    WHERE bean_id = ? AND roaster_id = ?
                """, (price, bean_id, auth_roaster_id))
                updated_count += cursor.rowcount

            if not error_occurred:
                conn.commit()
                if updated_count > 0:
                    flash(f'{updated_count} מחירים עודכנו בהצלחה', 'success')
                else:
                    flash('לא עודכנו מחירים. ייתכן שהפולים אינם שייכים לך או שלא נמצאו.', 'warning')

        except sqlite3.Error as e:
            conn.rollback()
            app.logger.error(f"Database error updating prices for roaster {auth_roaster_id}: {e}")
            flash("An error occurred while updating prices.", "danger")
        except Exception as e: # Catch other potential errors like ValueError from price conversion if added
            conn.rollback()
            app.logger.error(f"Unexpected error updating prices for roaster {auth_roaster_id}: {e}")
            flash("An unexpected error occurred.", "danger")

        # Redirect to the same page to refresh the data
        # Ensure selected_roaster_id for redirect is the one the user was viewing,
        # but the update operation was secured by auth_roaster_id.
        return redirect(url_for('edit_price', roaster_id=selected_roaster_id if selected_roaster_id else auth_roaster_id))

    conn.close()

    return render_template(
        'edit_price.html',
        roasters=roasters,
        beans=beans,
        selected_roaster_id=selected_roaster_id,
        selected_roaster_name=selected_roaster_name
    )

@app.context_processor
def inject_csrf_token():
    from flask_wtf.csrf import generate_csrf
    return dict(csrf_token=generate_csrf)

if __name__ == '__main__':
    # Remove debug=True before production
    app.run(host='0.0.0.0', port=5000)